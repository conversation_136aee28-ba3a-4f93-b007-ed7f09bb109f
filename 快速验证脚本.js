/**
 * 快速验证脚本 - 在浏览器控制台中运行
 * 用于快速验证状态同步问题修复效果
 */

(function() {
    'use strict';
    
    console.log('🔧 开始验证状态同步问题修复效果...\n');
    
    // 检查必要的函数是否存在
    function checkFunctions() {
        const requiredFunctions = [
            'updateProjectFileStatus',
            'updateProjectStatusFlags', 
            'smartRefreshProjectStatus',
            'updateProjectListItemStatus'
        ];
        
        const missingFunctions = requiredFunctions.filter(funcName => 
            typeof window[funcName] !== 'function'
        );
        
        if (missingFunctions.length > 0) {
            console.warn('⚠️ 以下函数不可用:', missingFunctions);
            return false;
        }
        
        console.log('✅ 所有必要函数都可用');
        return true;
    }
    
    // 检查项目数据是否存在
    function checkProjectData() {
        if (typeof projectsData === 'undefined' || !projectsData) {
            console.warn('⚠️ projectsData 不可用');
            return false;
        }
        
        console.log('✅ projectsData 可用');
        return true;
    }
    
    // 查找一个测试项目
    function findTestProject() {
        if (!projectsData) return null;
        
        for (const city in projectsData) {
            for (const type in projectsData[city]) {
                const projects = projectsData[city][type];
                if (projects && projects.length > 0) {
                    return projects[0]; // 返回第一个项目作为测试项目
                }
            }
        }
        return null;
    }
    
    // 模拟文件上传完成的状态更新
    function simulateFileUploadComplete(project) {
        console.log(`📁 模拟项目 ${project.id} 的文件上传完成...`);
        
        // 获取项目类型的文件模板
        const template = fileTemplates[project.bao_lx] || [];
        const requiredFileCount = template.length;
        
        console.log(`   项目类型: ${project.bao_lx}`);
        console.log(`   需要文件数: ${requiredFileCount}`);
        
        // 清空现有文件
        project.files = [];
        
        // 添加所有必需文件，状态设为"待审核"
        for (let i = 0; i < requiredFileCount; i++) {
            project.files.push({
                file_lx: i + 1,
                file_state: 0, // 待审核
                file_name: `test_file_${i + 1}.pdf`,
                file_hash: `hash_${i + 1}`,
                file_start: Math.floor(Date.now() / 1000),
                file_end: ''
            });
        }
        
        console.log(`   已添加 ${project.files.length} 个文件（状态：待审核）`);
        
        // 重新计算项目状态
        updateProjectStatusFlags(project);
        
        console.log('   状态计算结果:');
        console.log(`   - hasPendingSubmit: ${project.hasPendingSubmit}`);
        console.log(`   - hasRejected: ${project.hasRejected}`);
        console.log(`   - hasPending: ${project.hasPending}`);
        console.log(`   - bao_state: ${project.bao_state}`);
        
        // 确定应该显示的状态
        let expectedStatus = '';
        if (project.hasPendingSubmit) {
            expectedStatus = '待提交';
        } else if (project.hasRejected) {
            expectedStatus = '已退回';
        } else if (project.hasPending) {
            expectedStatus = '待审核';
        } else if (project.bao_state) {
            expectedStatus = '已完成';
        } else {
            expectedStatus = '进行中';
        }
        
        console.log(`   预期显示状态: ${expectedStatus}`);
        
        // 验证修复效果
        const isFixed = !project.hasPendingSubmit && !project.hasRejected && project.hasPending;
        console.log(`   修复验证: ${isFixed ? '✅ 通过' : '❌ 失败'}`);
        
        if (isFixed) {
            console.log('   🎉 状态同步问题已修复！所有文件上传完成后正确显示为"待审核"');
        } else {
            console.log('   ⚠️ 状态同步问题仍然存在，需要进一步检查');
        }
        
        return isFixed;
    }
    
    // 主验证流程
    function runQuickVerification() {
        console.log('='.repeat(60));
        console.log('📋 快速验证状态同步问题修复效果');
        console.log('='.repeat(60));
        
        // 步骤1：检查函数可用性
        if (!checkFunctions()) {
            console.log('❌ 验证失败：必要函数不可用');
            return false;
        }
        
        // 步骤2：检查项目数据
        if (!checkProjectData()) {
            console.log('❌ 验证失败：项目数据不可用');
            return false;
        }
        
        // 步骤3：查找测试项目
        const testProject = findTestProject();
        if (!testProject) {
            console.log('❌ 验证失败：未找到可用的测试项目');
            return false;
        }
        
        console.log(`🎯 使用测试项目: ${testProject.bao_name} (ID: ${testProject.id})`);
        
        // 步骤4：模拟文件上传完成并验证状态
        const isFixed = simulateFileUploadComplete(testProject);
        
        console.log('\n' + '='.repeat(60));
        if (isFixed) {
            console.log('🎉 验证结果：状态同步问题修复成功！');
            console.log('✅ 当所有必需文件都已上传且处于审核中时，项目状态正确显示为"待审核"');
        } else {
            console.log('❌ 验证结果：状态同步问题仍然存在');
            console.log('🔍 建议检查 updateProjectStatusFlags 函数的实现');
        }
        console.log('='.repeat(60));
        
        return isFixed;
    }
    
    // 提供手动测试函数
    window.quickVerifyStatusFix = runQuickVerification;
    
    // 自动运行验证
    setTimeout(() => {
        runQuickVerification();
    }, 100);
    
    console.log('\n💡 提示：您也可以随时运行 quickVerifyStatusFix() 来重新验证');
    
})();

/**
 * 使用说明：
 * 
 * 1. 在浏览器控制台中粘贴并运行此脚本
 * 2. 脚本会自动验证状态同步问题的修复效果
 * 3. 如果看到 "🎉 验证结果：状态同步问题修复成功！"，说明修复有效
 * 4. 可以随时运行 quickVerifyStatusFix() 来重新验证
 * 
 * 验证重点：
 * - 当所有必需文件都已上传且处于审核中时
 * - 项目状态应该显示为"待审核"而不是"待提交"
 * - hasPending=true, hasPendingSubmit=false, hasRejected=false
 */

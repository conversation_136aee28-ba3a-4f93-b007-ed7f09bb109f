//socket连接
window.WebSocketFlag = false;
var host = "ws://127.0.0.1:37896/"
window.socket = new WebSocket(host);

try {
    window.socket.onopen = function (msg) {
        window.WebSocketFlag = true;
    };
    window.socket.onmessage = function (msg) {
        if (typeof msg.data == "string") {
            var message = JSON.parse(msg.data);
            if ("VideoStream" == message['FuncName']) {
                //显示视频流不做输出
                var signinfo = document.getElementById("myCanvas");
                signinfo.src = "data:image/jpeg;base64," + message['VideoBase64'];
            } else {
                if ("ImgToBase64" == message['FuncName']) {
                    var base64WithoutPrefix = message.RetInfo.ImageBase64
                    const byteCharacters = atob(base64WithoutPrefix);
                    const byteArrays = [];

                    // 转换为 Uint8Array（二进制数据）
                    for (let i = 0; i < byteCharacters.length; i++) {
                        byteArrays.push(byteCharacters.charCodeAt(i));
                    }
                    // 创建 Blob 对象
                    const blob = new Blob([new Uint8Array(byteArrays)], { type: 'application/pdf' });

                    // 使用模板文件名创建文件
                    var my_file = new File([blob], window.templateFileName, { type: blob.type });
                    uploadFile(window.bao_id, window.file_id, my_file, window.fileElement, true);
                }
                else if ("SaveToPDF" == message['FuncName']) {

                    if (message.RetInfo != "成功") {
                        alert("转换PDF失败");
                        return
                    }
                    socket.send(JSON.stringify({
                        'FuncName': 'ImgToBase64',
                        'FileName': 'D:\\savepdf.pdf',
                        'DeleteImg': 1
                    }));
                }
            }
        }
    };
    window.socket.onclose = function (msg) {
        var myimg = document.getElementById("myCanvas");

    };
}
catch (ex) {
    alert(ex);
}

// 项目状态更新WebSocket连接
let statusSocket = null;
let statusSocketReconnectTimer = null;
const STATUS_SOCKET_URL = `ws://${window.location.host}/socket.io/`;

/**
 * 初始化项目状态更新WebSocket连接
 */
function initStatusWebSocket() {
    try {
        // 使用Socket.IO客户端连接
        if (typeof io !== 'undefined') {
            statusSocket = io();

            statusSocket.on('connect', function() {
                console.log('项目状态WebSocket连接成功');
                // 清除重连定时器
                if (statusSocketReconnectTimer) {
                    clearTimeout(statusSocketReconnectTimer);
                    statusSocketReconnectTimer = null;
                }
            });

            statusSocket.on('disconnect', function() {
                console.log('项目状态WebSocket连接断开');
                // 设置重连定时器
                if (!statusSocketReconnectTimer) {
                    statusSocketReconnectTimer = setTimeout(() => {
                        console.log('尝试重新连接项目状态WebSocket...');
                        initStatusWebSocket();
                    }, 3000);
                }
            });

            // 监听全局项目更新事件
            statusSocket.on('global_project_update', function(data) {
                console.log('收到项目更新事件:', data);
                handleProjectUpdateEvent(data);
            });

            // 监听特定项目更新事件
            statusSocket.on('project_update', function(data) {
                console.log('收到特定项目更新事件:', data);
                handleProjectUpdateEvent(data);
            });

        } else {
            console.warn('Socket.IO客户端库未加载，无法建立WebSocket连接');
        }
    } catch (error) {
        console.error('初始化项目状态WebSocket连接失败:', error);
    }
}

/**
 * 处理项目更新事件
 * @param {Object} data - 更新事件数据
 */
function handleProjectUpdateEvent(data) {
    const { project_id, type, data: updateData } = data;

    switch (type) {
        case 'file_uploaded':
            // 文件上传完成事件
            if (updateData && updateData.file_info) {
                const { file_type, file_name, file_state } = updateData.file_info;
                updateProjectFileStatus(project_id, file_type, file_state, null, file_name, null, true);
                showNotification(`项目文件已更新: ${file_name}`, false);
            }
            break;

        case 'file_reviewed':
            // 文件审核完成事件
            if (updateData && updateData.file_info) {
                const { file_type, file_state, reason } = updateData.file_info;
                updateProjectFileStatus(project_id, file_type, file_state, reason, null, null, true);

                const statusText = file_state === 1 ? '审核通过' : '已退回';
                showNotification(`文件${statusText}`, false);
            }
            break;

        case 'project_completed':
            // 项目完成事件
            showNotification('项目已完成审核！', false);
            // 刷新当前项目列表
            if (currentCity && currentType) {
                setTimeout(() => {
                    loadProjects(projectsData, currentCity, currentType);
                }, 500);
            }
            break;

        default:
            console.log('未知的项目更新事件类型:', type);
    }
}

// 用户详情侧边栏控制功能
function initUserinfoSidebar() {
    const userinfoScreen = document.getElementById('userinfo-screen');
    const toggleBtn = document.getElementById('userinfo-toggle-btn');

    if (!userinfoScreen || !toggleBtn) {

        return false;
    }

    // 强制重置为默认折叠状态（移除任何可能的expanded类）
    userinfoScreen.classList.remove('expanded');


    // 获取本地存储的状态，如果没有设置过，默认为折叠状态
    const savedState = localStorage.getItem('userinfoSidebarCollapsed');
    const isCollapsed = savedState === null ? true : savedState === 'true';



    // 在桌面端应用状态
    if (window.innerWidth > 768) {
        // 强制确保默认为折叠状态
        userinfoScreen.classList.remove('expanded');

        // 只有明确设置为展开时才添加expanded类
        if (!isCollapsed) {
            userinfoScreen.classList.add('expanded');
            updateToggleButtonIcon(toggleBtn, false);

        } else {
            updateToggleButtonIcon(toggleBtn, true);

        }

        // 如果是首次访问（没有保存状态），设置默认折叠状态到本地存储
        if (savedState === null) {
            localStorage.setItem('userinfoSidebarCollapsed', 'true');

        }
    } else {
        // 移动端确保展开状态
        userinfoScreen.classList.add('expanded');
        updateToggleButtonIcon(toggleBtn, false);

    }

    // 移除之前的事件监听器（如果存在）
    const existingHandler = toggleBtn._clickHandler;
    if (existingHandler) {
        toggleBtn.removeEventListener('click', existingHandler);
    }

    // 绑定折叠/展开按钮事件
    const clickHandler = function (e) {
        e.preventDefault();
        e.stopPropagation();
        toggleUserinfoSidebar();
    };

    toggleBtn.addEventListener('click', clickHandler);
    toggleBtn._clickHandler = clickHandler; // 保存引用以便后续移除

    // 监听窗口大小变化，在移动端自动展开
    window.addEventListener('resize', function () {
        if (window.innerWidth <= 768) {
            // 移动端强制展开
            if (!userinfoScreen.classList.contains('expanded')) {
                userinfoScreen.classList.add('expanded');
                updateToggleButtonIcon(toggleBtn, false);
            }
        }

        // 重置按钮位置，让CSS控制
        resetButtonPosition(toggleBtn);
    });


    return true;
}

// 切换用户详情侧边栏状态
function toggleUserinfoSidebar() {
    const userinfoScreen = document.getElementById('userinfo-screen');
    const toggleBtn = document.getElementById('userinfo-toggle-btn');

    if (!userinfoScreen || !toggleBtn) {
        return;
    }

    // 在移动端不执行折叠操作
    if (window.innerWidth <= 768) {
        return;
    }

    const isExpanded = userinfoScreen.classList.contains('expanded');

    if (isExpanded) {
        // 折叠侧边栏
        userinfoScreen.classList.remove('expanded');
        updateToggleButtonIcon(toggleBtn, true);
        localStorage.setItem('userinfoSidebarCollapsed', 'true');

        // 触发自定义事件，通知其他组件侧边栏已折叠
        window.dispatchEvent(new CustomEvent('userinfoSidebarCollapsed'));
    } else {
        // 展开侧边栏
        userinfoScreen.classList.add('expanded');
        updateToggleButtonIcon(toggleBtn, false);
        localStorage.setItem('userinfoSidebarCollapsed', 'false');

        // 触发自定义事件，通知其他组件侧边栏已展开
        window.dispatchEvent(new CustomEvent('userinfoSidebarExpanded'));
    }
}

// 更新折叠按钮图标
function updateToggleButtonIcon(button, isCollapsed) {
    const icon = button.querySelector('i');
    if (icon) {
        if (isCollapsed) {
            icon.className = 'bi bi-chevron-right';
            button.title = '展开用户详情';
        } else {
            icon.className = 'bi bi-chevron-left';
            button.title = '折叠用户详情';
        }
    }

    // 重置按钮的内联样式，让CSS控制定位
    resetButtonPosition(button);
}

// 重置按钮位置，移除JavaScript设置的内联样式
function resetButtonPosition(button) {
    // 移除所有可能的内联定位样式，让CSS控制
    button.style.position = '';
    button.style.right = '';
    button.style.top = '';
    button.style.left = '';
    button.style.transform = '';


}

document.addEventListener('DOMContentLoaded', function () {
    // 确保搜索框初始状态为隐藏
    const projectSearchInput = document.getElementById('project-search');
    if (projectSearchInput) {
        projectSearchInput.classList.add('d-none');
    }

    // 立即尝试初始化用户详情侧边栏
    let initAttempts = 0;
    const maxAttempts = 10;

    function tryInitSidebar() {
        initAttempts++;
        if (initUserinfoSidebar()) {

        } else if (initAttempts < maxAttempts) {

            setTimeout(tryInitSidebar, 100);
        } else {

        }
    }

    // 立即尝试初始化
    tryInitSidebar();

    // 创建并显示加载指示器
    const loadingIndicator = document.createElement('div');
    loadingIndicator.id = 'loading-indicator';
    loadingIndicator.innerHTML = `
        <div class="loading-container">
            <div class="loading-spinner"></div>
            <div class="loading-text">数据加载中...</div>
        </div>
    `;
    document.body.appendChild(loadingIndicator);

    // 添加加载指示器的样式
    const style = document.createElement('style');
    style.textContent = `
        #loading-indicator {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .loading-container {
            text-align: center;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #00706b;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            margin: 0 auto 15px;
            animation: spin 1s linear infinite;
        }
        .loading-text {
            font-size: 18px;
            color: #333;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(style);

    // 模拟用户数据生成函数
    function generateMockUserData(usercode) {
        if (!usercode) return null;

        // 基于户号生成模拟数据
        const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'];
        const addresses = [
            '北京市海淀区西二旗街道',
            '河南省驻马店市驿城区',
            '河南省驻马店市确山县',
            '河南省驻马店市泌阳县',
            '河南省驻马店市汝南县',
            '河南省驻马店市平舆县',
            '河南省驻马店市新蔡县',
            '河南省驻马店市上蔡县'
        ];
        const banks = ['中国银行', '工商银行', '建设银行', '农业银行', '交通银行', '招商银行'];

        // 使用户号的数字部分作为种子
        const seed = parseInt(usercode.replace(/\D/g, '')) || 1;

        return {
            name: names[seed % names.length],
            phone: `138${String(seed).padStart(8, '0').slice(0, 8)}`,
            usercode: usercode,
            address: addresses[seed % addresses.length],
            id_card: `41${String(seed).padStart(16, '0').slice(0, 16)}`,
            bank_name: banks[seed % banks.length],
            bank_card: `6222${String(seed).padStart(15, '0').slice(0, 15)}`
        };
    }

    // 生成真实工单流程数据函数
    function generateRealGongdanData(project) {
        if (!project) return [];

        // 定义工单流程步骤和对应的时间字段
        const stepDefinitions = [
            { name: '业务受理', timeField: 'sj_ywsl', description: '客户申请受理' },
            { name: '现场勘察', timeField: 'sj_xckc', description: '现场勘察和测量' },
            { name: '方案答复', timeField: 'sj_fadf', description: '供电方案设计和答复' },
            { name: '竣工验收', timeField: 'sj_jgys', description: '工程竣工验收' },
            { name: '装表送电', timeField: 'sj_zbsd', description: '装表接电送电' },
            { name: '项目归档', timeField: 'sj_gdsj', description: '项目资料归档' }
        ];

        const gongdanData = [];

        stepDefinitions.forEach(step => {
            const timeValue = project[step.timeField];
            let stepStatus = '数据未录入';
            let timeDisplay = '数据未录入';
            let statusClass = 'not-entered';

            if (timeValue && timeValue !== '' && timeValue !== null && timeValue !== undefined) {
                // 如果时间字段有值，转换为可读格式
                try {
                    let date;

                    if (typeof timeValue === 'string') {
                        if (timeValue.includes('-') || timeValue.includes('/')) {
                            // 日期字符串格式 (YYYY-MM-DD 或 YYYY/MM/DD)
                            date = new Date(timeValue);
                        } else if (!isNaN(timeValue)) {
                            // 字符串形式的时间戳
                            const timestamp = parseInt(timeValue);
                            // 判断是秒级还是毫秒级时间戳
                            date = timestamp > 1000000000000 ? new Date(timestamp) : new Date(timestamp * 1000);
                        }
                    } else if (typeof timeValue === 'number') {
                        // 数字形式的时间戳
                        // 判断是秒级还是毫秒级时间戳
                        date = timeValue > 1000000000000 ? new Date(timeValue) : new Date(timeValue * 1000);
                    }

                    if (date && !isNaN(date.getTime())) {
                        timeDisplay = date.toLocaleString('zh-CN', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit'
                        });
                        stepStatus = '已完成';
                        statusClass = 'completed';

                    } else {

                    }
                } catch (e) {

                }
            }

            gongdanData.push({
                stepName: step.name,
                stepDescription: step.description,
                stepStatus: stepStatus,
                timeDisplay: timeDisplay,
                statusClass: statusClass,
                timeField: step.timeField
            });
        });

        return gongdanData;
    }

    const my_gds = {
        "环城": [{
            "mgtOrgName": "城区直供",
            "mgtOrgCode": "414150101"
        }, {
            "mgtOrgName": "郊区直供",
            "mgtOrgCode": "414150102"
        }, {
            "mgtOrgName": "老街中心供电所",
            "mgtOrgCode": "414150104"
        }, {
            "mgtOrgName": "香山中心供电所",
            "mgtOrgCode": "414150107"
        }, {
            "mgtOrgName": "胡庙中心供电所",
            "mgtOrgCode": "414150108"
        }, {
            "mgtOrgName": "罗店",
            "mgtOrgCode": "414150109"
        }, {
            "mgtOrgName": "刘阁中心供电所",
            "mgtOrgCode": "414150110"
        }, {
            "mgtOrgName": "朱古洞中心供电所",
            "mgtOrgCode": "414150112"
        }, {
            "mgtOrgName": "关王庙中心供电所",
            "mgtOrgCode": "414150113"
        }, {
            "mgtOrgName": "沙河店中心供电所",
            "mgtOrgCode": "414150116"
        }, {
            "mgtOrgName": "水屯供电所",
            "mgtOrgCode": "414150118"
        }],
        "城区": [{
            "mgtOrgName": "城区专线",
            "mgtOrgCode": "414150201"
        }, {
            "mgtOrgName": "城东用电服务班",
            "mgtOrgCode": "414150202"
        }, {
            "mgtOrgName": "城中用电服务班",
            "mgtOrgCode": "414150203"
        }, {
            "mgtOrgName": "城南用电服务班",
            "mgtOrgCode": "414150204"
        }, {
            "mgtOrgName": "城北用电服务班",
            "mgtOrgCode": "414150205"
        }, {
            "mgtOrgName": "关王庙中心供电所",
            "mgtOrgCode": "414150206"
        }],
        "泌阳": [{
            "mgtOrgName": "城区供电中心",
            "mgtOrgCode": "414152101"
        }, {
            "mgtOrgName": "花园中心供电所",
            "mgtOrgCode": "414152102"
        }, {
            "mgtOrgName": "杨家集中心供电所",
            "mgtOrgCode": "414152104"
        }, {
            "mgtOrgName": "盘古中心供电所",
            "mgtOrgCode": "414152105"
        }, {
            "mgtOrgName": "双庙中心供电所",
            "mgtOrgCode": "414152106"
        }, {
            "mgtOrgName": "羊册供电所",
            "mgtOrgCode": "414152109"
        }, {
            "mgtOrgName": "郭集供电所",
            "mgtOrgCode": "414152110"
        }, {
            "mgtOrgName": "春水中心供电所",
            "mgtOrgCode": "414152112"
        }, {
            "mgtOrgName": "石材区中心供电所",
            "mgtOrgCode": "414152113"
        }, {
            "mgtOrgName": "付庄中心供电所",
            "mgtOrgCode": "414152114"
        }, {
            "mgtOrgName": "马谷田中心供电所",
            "mgtOrgCode": "414152116"
        }, {
            "mgtOrgName": "下碑寺中心供电所",
            "mgtOrgCode": "414152121"
        }, {
            "mgtOrgName": "铜山中心供电所",
            "mgtOrgCode": "414152124"
        }, {
            "mgtOrgName": "营业室",
            "mgtOrgCode": "414152125"
        }],
        "平舆": [{
            "mgtOrgName": "城区供电中心",
            "mgtOrgCode": "414152201"
        }, {
            "mgtOrgName": "东皇庙中心供电所",
            "mgtOrgCode": "414152202"
        }, {
            "mgtOrgName": "玉皇庙供电所",
            "mgtOrgCode": "414152203"
        }, {
            "mgtOrgName": "王岗中心供电所",
            "mgtOrgCode": "414152207"
        }, {
            "mgtOrgName": "庙湾中心供电所",
            "mgtOrgCode": "414152208"
        }, {
            "mgtOrgName": "西洋店中心供电所",
            "mgtOrgCode": "414152209"
        }, {
            "mgtOrgName": "和店中心供电所",
            "mgtOrgCode": "414152211"
        }, {
            "mgtOrgName": "杨埠中心供电所",
            "mgtOrgCode": "414152212"
        }, {
            "mgtOrgName": "阳城中心供电所",
            "mgtOrgCode": "414152215"
        }, {
            "mgtOrgName": "万冢中心供电所",
            "mgtOrgCode": "414152218"
        }],
        "汝南": [{
            "mgtOrgName": "城区供电中心",
            "mgtOrgCode": "414152301"
        }, {
            "mgtOrgName": "集聚区中心供电所",
            "mgtOrgCode": "414152302"
        }, {
            "mgtOrgName": "和孝中心供电所",
            "mgtOrgCode": "414152304"
        }, {
            "mgtOrgName": "王岗中心供电所",
            "mgtOrgCode": "414152305"
        }, {
            "mgtOrgName": "老君庙中心供电所",
            "mgtOrgCode": "414152309"
        }, {
            "mgtOrgName": "梁祝供电所",
            "mgtOrgCode": "414152310"
        }, {
            "mgtOrgName": "金铺中心供电所",
            "mgtOrgCode": "414152312"
        }, {
            "mgtOrgName": "官庄供电所",
            "mgtOrgCode": "414152316"
        }, {
            "mgtOrgName": "三桥供电所",
            "mgtOrgCode": "414152318"
        }, {
            "mgtOrgName": "三门闸中心供电所",
            "mgtOrgCode": "414152319"
        }, {
            "mgtOrgName": "留盆供电所",
            "mgtOrgCode": "414152320"
        }],
        "上蔡": [{
            "mgtOrgName": "蔡都供电中心",
            "mgtOrgCode": "414152401"
        }, {
            "mgtOrgName": "塔桥中心供电所",
            "mgtOrgCode": "414152402"
        }, {
            "mgtOrgName": "五龙中心供电所",
            "mgtOrgCode": "414152403"
        }, {
            "mgtOrgName": "杨集中心供电所",
            "mgtOrgCode": "414152406"
        }, {
            "mgtOrgName": "大路李中心供电所",
            "mgtOrgCode": "414152409"
        }, {
            "mgtOrgName": "东洪供电所",
            "mgtOrgCode": "414152411"
        }, {
            "mgtOrgName": "朱里中心供电所",
            "mgtOrgCode": "414152412"
        }, {
            "mgtOrgName": "西洪中心供电所",
            "mgtOrgCode": "414152414"
        }, {
            "mgtOrgName": "华陂中心供电所",
            "mgtOrgCode": "414152416"
        }, {
            "mgtOrgName": "和店中心供电所",
            "mgtOrgCode": "414152418"
        }, {
            "mgtOrgName": "蔡沟中心供电所",
            "mgtOrgCode": "414152419"
        }, {
            "mgtOrgName": "黄埠中心供电所",
            "mgtOrgCode": "414152423"
        }, {
            "mgtOrgName": "环城供电所",
            "mgtOrgCode": "414152424"
        }, {
            "mgtOrgName": "上蔡县产业集聚区供电所",
            "mgtOrgCode": "414152425"
        }],
        "遂平": [{
            "mgtOrgName": "常庄中心供电所",
            "mgtOrgCode": "414152502"
        }, {
            "mgtOrgName": "沈寨中心供电所",
            "mgtOrgCode": "414152503"
        }, {
            "mgtOrgName": "和兴中心供电所",
            "mgtOrgCode": "414152504"
        }, {
            "mgtOrgName": "嵖岈山中心供电所",
            "mgtOrgCode": "414152510"
        }, {
            "mgtOrgName": "玉山中心供电所",
            "mgtOrgCode": "414152513"
        }, {
            "mgtOrgName": "张台中心供电所",
            "mgtOrgCode": "414152515"
        }, {
            "mgtOrgName": "车站中心供电所",
            "mgtOrgCode": "414152516"
        }, {
            "mgtOrgName": "灈阳供电中心",
            "mgtOrgCode": "414152519"
        }, {
            "mgtOrgName": "莲花湖供电中心",
            "mgtOrgCode": "414152520"
        }],
        "西平": [{
            "mgtOrgName": "柏城供电中心",
            "mgtOrgCode": "414152601"
        }, {
            "mgtOrgName": "盆尧中心供电所",
            "mgtOrgCode": "414152605"
        }, {
            "mgtOrgName": "二郎中心供电所",
            "mgtOrgCode": "414152606"
        }, {
            "mgtOrgName": "出山中心供电所",
            "mgtOrgCode": "414152613"
        }, {
            "mgtOrgName": "师灵中心供电所",
            "mgtOrgCode": "414152616"
        }, {
            "mgtOrgName": "专探中心供电所",
            "mgtOrgCode": "414152619"
        }, {
            "mgtOrgName": "柏亭供电中心",
            "mgtOrgCode": "414152620"
        }, {
            "mgtOrgName": "老王坡中心供电所",
            "mgtOrgCode": "414152621"
        }, {
            "mgtOrgName": "西平县产业集聚区供电所",
            "mgtOrgCode": "414152622"
        }],
        "新蔡": [{
            "mgtOrgName": "陈店中心供电所",
            "mgtOrgCode": "414152701"
        }, {
            "mgtOrgName": "佛阁寺中心供电所",
            "mgtOrgCode": "414152703"
        }, {
            "mgtOrgName": "城区供电中心",
            "mgtOrgCode": "414152704"
        }, {
            "mgtOrgName": "涧头中心供电所",
            "mgtOrgCode": "414152710"
        }, {
            "mgtOrgName": "栎城中心供电所",
            "mgtOrgCode": "414152713"
        }, {
            "mgtOrgName": "园区中心供电所",
            "mgtOrgCode": "414152717"
        }, {
            "mgtOrgName": "宋岗中心供电所",
            "mgtOrgCode": "414152718"
        }, {
            "mgtOrgName": "孙召中心供电所",
            "mgtOrgCode": "414152719"
        }, {
            "mgtOrgName": "棠村中心供电所",
            "mgtOrgCode": "414152720"
        }, {
            "mgtOrgName": "砖店中心供电所",
            "mgtOrgCode": "414152723"
        }, {
            "mgtOrgName": "古吕中心供电所",
            "mgtOrgCode": "414152724"
        }],
        "确山": [{
            "mgtOrgName": "城区供电中心",
            "mgtOrgCode": "414152801"
        }, {
            "mgtOrgName": "刘店中心供电所",
            "mgtOrgCode": "414152803"
        }, {
            "mgtOrgName": "三里河中心供电所",
            "mgtOrgCode": "414152804"
        }, {
            "mgtOrgName": "竹沟中心供电所",
            "mgtOrgCode": "414152807"
        }, {
            "mgtOrgName": "任店中心供电所",
            "mgtOrgCode": "414152809"
        }, {
            "mgtOrgName": "新安店中心供电所",
            "mgtOrgCode": "414152810"
        }, {
            "mgtOrgName": "双河中心供电所",
            "mgtOrgCode": "414152812"
        }, {
            "mgtOrgName": "营销部",
            "mgtOrgCode": "414152817"
        }, {
            "mgtOrgName": "园区供电中心",
            "mgtOrgCode": "414152818"
        }],
        "正阳": [{
            "mgtOrgName": "城区供电中心",
            "mgtOrgCode": "414152901"
        }, {
            "mgtOrgName": "铜钟中心供电所",
            "mgtOrgCode": "414152904"
        }, {
            "mgtOrgName": "兰青中心供电所",
            "mgtOrgCode": "414152905"
        }, {
            "mgtOrgName": "彭桥中心供电所",
            "mgtOrgCode": "414152907"
        }, {
            "mgtOrgName": "慎水中心供电所",
            "mgtOrgCode": "414152911"
        }, {
            "mgtOrgName": "汝南埠中心供电所",
            "mgtOrgCode": "414152912"
        }, {
            "mgtOrgName": "雷寨中心供电所",
            "mgtOrgCode": "414152913"
        }, {
            "mgtOrgName": "正阳县产业集聚区中心供电所",
            "mgtOrgCode": "414152917"
        }, {
            "mgtOrgName": "熊寨中心供电所",
            "mgtOrgCode": "414152918"
        }, {
            "mgtOrgName": "吕河中心供电所",
            "mgtOrgCode": "414152919"
        }, {
            "mgtOrgName": "寒冻中心供电所",
            "mgtOrgCode": "414152920"
        }, {
            "mgtOrgName": "营销部",
            "mgtOrgCode": "414152923"
        }]
    }

    // 定义不同项目类型的文件模板
    const fileTemplates = {
        // 高压模板
        "高压用户": [
            { file_id: 1, name: "用电登记表（包括一次性告知书）", status: "pending-submit" },
            { file_id: 2, name: "用电人有效身份证件", status: "pending-submit" },
            { file_id: 3, name: "用电地址物权证件", status: "pending-submit" },
            { file_id: 4, name: "电工程项目批准文件", status: "pending-submit" },
            { file_id: 5, name: "授权委托书（根据是否有委托产生而定）", status: "pending-submit" },
            { file_id: 6, name: "现场勘查单", status: "pending-submit" },
            { file_id: 7, name: "供电方案答复单", status: "pending-submit" },
            { file_id: 8, name: "设计资质证书（复印件）*", status: "pending-submit" },
            { file_id: 9, name: "承装（修、试）电力设施许可证（复印件）、试验单位资质证明（复印件）*", status: "pending-submit" },
            { file_id: 10, name: "竣工归档资料（竣工图纸-蓝图）*", status: "pending-submit" },
            { file_id: 16, name: "竣工归档资料（电气设备出厂合格证书）*", status: "pending-submit" },
            { file_id: 17, name: "竣工归档资料（电气设备交接试验记录）*", status: "pending-submit" },
            { file_id: 11, name: "电能计量装置装（拆）单", status: "pending-submit" },
            { file_id: 12, name: "受电工程竣工报验单", status: "pending-submit" },
            { file_id: 13, name: "受电工程竣工检验意见单", status: "pending-submit" },
            { file_id: 14, name: "供用电合同及其附件、相关补充协议", status: "pending-submit" },
            { file_id: 15, name: "送（停）电单", status: "pending-submit" }
        ],
        // 低压模板
        "低压用户": [
            { file_id: 1, name: "用电登记表（包含一次性告知书）", status: "pending-submit" },
            { file_id: 2, name: "小微企业截图", status: "pending-submit" },
            { file_id: 3, name: "用电人有效身份证件", status: "pending-submit" },
            { file_id: 4, name: "用电地址物权证件", status: "pending-submit" },
            { file_id: 5, name: "授权委托书（根据是否有委托产生而定）", status: "pending-submit" },
            { file_id: 6, name: "低压现场勘查单", status: "pending-submit" },
            { file_id: 7, name: "计量装置装（拆）单", status: "pending-submit" },
            { file_id: 8, name: "现场装表照片", status: "pending-submit" },
            { file_id: 9, name: "送（停）电单", status: "pending-submit" },
            { file_id: 10, name: "供用电合同", status: "pending-submit" }
        ],
        // 光伏模板
        "光伏低压自然人": [
            { file_id: 1, name: "分布式电源项目并网申请表", status: "pending-submit" },
            { file_id: 2, name: "自投承诺书", status: "pending-submit" },
            { file_id: 3, name: "居民身份证", status: "pending-submit" },
            { file_id: 4, name: "产权合法证明", status: "pending-submit" },
            { file_id: 5, name: "分布式电源项目现场勘察意见单", status: "pending-submit" },
            { file_id: 6, name: "分布式电源项目接入系统方案项目业主（用户）确认单", status: "pending-submit" },
            { file_id: 7, name: "购售电合同及附件", status: "pending-submit" },
            { file_id: 8, name: "并网验收意见单", status: "pending-submit" },
            { file_id: 9, name: "电能计量装置装(拆)单", status: "pending-submit" },
            { file_id: 10, name: "设备（包括光伏组件、逆变器）购置发票*", status: "pending-submit" },
            { file_id: 11, name: "光伏项目备案资料", status: "pending-submit" },
            { file_id: 12, name: "安全生产许可证", status: "pending-submit" },
            { file_id: 13, name: "电气设备交接试验记录", status: "pending-submit" },
            { file_id: 14, name: "建筑企业资质证书", status: "pending-submit" },
            { file_id: 15, name: "主要设备技术参数、型式认证报告或", status: "pending-submit" },
            { file_id: 16, name: "施工单位资质，承装（修、试）电力设施许可证（复印件）、试验单位资质证明（复印件）*", status: "pending-submit" },
            { file_id: 17, name: "现场装表照片", status: "pending-submit" },
            { file_id: 18, name: "低压分布式光伏接入系统方案（本地留存）", status: "pending-submit" }
        ],
        // 光伏模板
        "光伏低压非自然人": [
            { file_id: 1, name: "分布式电源项目并网申请表", status: "pending-submit" },
            { file_id: 2, name: "居民身份证", status: "pending-submit" },
            { file_id: 3, name: "营业执照", status: "pending-submit" },
            { file_id: 4, name: "租赁协议、租赁方产权证明", status: "pending-submit" },
            { file_id: 5, name: "光伏项目备案资料", status: "pending-submit" },
            { file_id: 6, name: "分布式电源项目现场勘察意见单", status: "pending-submit" },
            { file_id: 7, name: "分布式电源项目接入系统方案项目业主（用户）确认单", status: "pending-submit" },
            { file_id: 8, name: "并网检验申请单", status: "pending-submit" },
            { file_id: 9, name: "并网验收意见单", status: "pending-submit" },
            { file_id: 10, name: "电能计量装置装(拆)单", status: "pending-submit" },
            { file_id: 11, name: "设备（包括光伏组件、逆变器）购置发票*", status: "pending-submit" },
            { file_id: 12, name: "安全生产许可证", status: "pending-submit" },
            { file_id: 13, name: "电气设备交接试验记录", status: "pending-submit" },
            { file_id: 14, name: "建筑企业资质证书", status: "pending-submit" },
            { file_id: 15, name: "主要设备技术参数、型式认证报告或", status: "pending-submit" },
            { file_id: 16, name: "施工单位资质，承装（修、试）电力设施许可证（复印件）、试验单位资质证明（复印件）*", status: "pending-submit" },
            { file_id: 17, name: "现场装表照片", status: "pending-submit" },
            { file_id: 18, name: "并网调度协议", status: "pending-submit" },
            { file_id: 19, name: "购售电合同及附件", status: "pending-submit" },
            { file_id: 20, name: "低压分布式光伏接入系统方案（本地留存）", status: "pending-submit" }
        ],
        // 光伏模板
        "光伏高压": [
            { file_id: 1, name: "政府职能部门有关本项目立项的批复*", status: "pending-submit" },
            { file_id: 2, name: "电网承载力评估查询结果图*", status: "pending-submit" },
            { file_id: 3, name: "分布式电源项目并网申请表*", status: "pending-submit" },
            { file_id: 4, name: "居民身份证", status: "pending-submit" },
            { file_id: 5, name: "营业执照", status: "pending-submit" },
            { file_id: 6, name: "产权证*", status: "pending-submit" },
            { file_id: 7, name: "租赁协议、租赁方产权证明*", status: "pending-submit" },
            { file_id: 8, name: "分布式电源项目现场勘察意见单*", status: "pending-submit" },
            { file_id: 9, name: "分布式电源项目接入系统方案项目业主（用户）确认单*", status: "pending-submit" },
            { file_id: 10, name: "分布式电源项目接入系统方案", status: "pending-submit" },
            { file_id: 11, name: "关于XXX项目接入电网意见的函", status: "pending-submit" },
            { file_id: 12, name: "竣工归档资料(包括竣工图纸、电气设备出厂合格证书、电气设备交接试验记录)*", status: "pending-submit" },
            { file_id: 13, name: "客户工程竣工检验意见单*", status: "pending-submit" },
            { file_id: 14, name: "设备（包括光伏组件、逆变器）购置发票*", status: "pending-submit" },
            { file_id: 15, name: "计量装置装（拆）单*", status: "pending-submit" },
            { file_id: 16, name: "并网验收意见单*", status: "pending-submit" },
            { file_id: 17, name: "购售电合同及附件", status: "pending-submit" },
            { file_id: 18, name: "高压电能计量装接单", status: "pending-submit" },
            { file_id: 19, name: "表计示数照片", status: "pending-submit" },
            { file_id: 20, name: "计量箱封印(锁)及外观", status: "pending-submit" }
        ]
    };
    fileTemplates.华宇高压用户 = fileTemplates.高压用户;

    // 状态映射
    const statusMap = {
        0: "pending",
        1: "approved",
        2: "rejected",
        3: "pending-submit"
    };

    // 状态文本映射
    const statusTextMap = {
        "pending": "审核中",
        "approved": "已通过",
        "rejected": "已退回",
        "pending-submit": "待提交"
    };

    // 状态颜色映射
    const statusColorMap = {
        "pending": "blue",
        "approved": "green",
        "rejected": "yellow",
        "pending-submit": "red"
    };

    // 文件预览类型
    const previewableTypes = ['pdf'];

    // API基础URL
    const API_BASE_URL = 'http://39.164.220.187:5000';
    //const API_BASE_URL = 'http://127.0.0.1:5000';

    // 获取token
    const token = localStorage.getItem('access_token');
    const user_city = localStorage.getItem('city');
    const user_name = localStorage.getItem('username');
    if (!token) {
        window.location.href = './login.html';
    }

    // 显示用户信息在header-title中
    function displayUserInfo() {
        const userInfoElement = document.getElementById('user-info');

        if (userInfoElement && user_city && user_name) {
            let displayText = '';
            if (user_city === 'VIP') {
                displayText = `超级管理员 - ${user_name}`;
            } else {
                displayText = `${user_city} - ${user_name}`;
            }
            userInfoElement.textContent = displayText;
        } else {
            if (userInfoElement) {
                userInfoElement.textContent = '未登录';
            }
        }
    }

    // 退出登录功能
    function logout() {
        // 清除本地存储的用户信息
        localStorage.removeItem('access_token');
        localStorage.removeItem('city');
        localStorage.removeItem('username');

        // 显示退出提示
        showNotification('已退出登录');

        // 延迟跳转到登录页面
        setTimeout(() => {
            window.location.href = './login.html';
        }, 1000);
    }

    // 绑定退出登录按钮事件
    let logoutEventBound = false; // 防止重复绑定
    function bindLogoutEvent() {
        if (logoutEventBound) return; // 如果已经绑定过，直接返回

        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', function () {
                if (confirm('确定要退出登录吗？')) {
                    logout();
                }
            });
            logoutEventBound = true; // 标记已绑定
        }
    }

    // 立即尝试显示用户信息和绑定事件
    setTimeout(() => {
        displayUserInfo();
        bindLogoutEvent();
    }, 100);

    // 全局存储项目数据
    let projectsData = null;


    // 当前选中的文件和项目
    let currentSelectedProject = null;
    let currentSelectedFile = null;

    // 文件下载状态标志
    let isDownloading = false;
    let isGpUploading = false;

    // 使用fetch获取项目数据
    fetch(`${API_BASE_URL}/baos`, {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
        .then(response => response.json())
        .then(data => {
            projectsData = data.cont;
            // 处理数据
            processData(projectsData);
            initNavigation(projectsData);

            // 显示用户信息和绑定退出登录事件
            displayUserInfo();
            bindLogoutEvent();

            // 隐藏加载指示器
            document.getElementById('loading-indicator').style.display = 'none';
        })
        .catch(error => {
            showNotification('获取数据失败，请检查网络连接', true);
            localStorage.setItem('access_token', '')
            localStorage.setItem('city', '');
            localStorage.setItem('username', '');
            window.location.href = './login.html';
            // 即使出错也隐藏加载指示器（虽然会重定向到登录页）
            document.getElementById('loading-indicator').style.display = 'none';
        });

    // 处理项目数据 
    function processData(projectsData) {
        Object.keys(projectsData).forEach(city => {
            Object.keys(projectsData[city]).forEach(type => {
                const projects = projectsData[city][type];
                projects.forEach(project => {
                    // 初始化项目状态
                    project.hasPendingSubmit = false;
                    project.hasRejected = false;
                    project.hasPending = false;

                    // 根据项目类型获取对应的文件模板
                    const template = fileTemplates[type] || [];
                    // 创建项目文件模板（深拷贝）
                    project.filesTemplate = JSON.parse(JSON.stringify(template));

                    // 如果有实际文件，更新文件状态
                    if (project.files && project.files.length > 0) {
                        project.files.forEach(file => {
                            // 查找模板中对应的文件
                            const templateFile = project.filesTemplate.find(f => f.file_id === file.file_lx);
                            if (templateFile) {
                                templateFile.status = statusMap[file.file_state] || "pending-submit";
                                if (file.file_bz) {
                                    templateFile.reason = file.file_bz;
                                }
                                // 存储文件ID
                                templateFile.id = file.id;
                                // 存储文件hash
                                templateFile.file_hash = file.file_hash || '';
                                // 存储文件名
                                templateFile.actualName = file.file_name;
                            }


                        });

                    }

                    // 设置项目状态
                    project.status = project.bao_state === false ? "进行中" : "已完成";
                    // 更新项目状态标记
                    project.hasPendingSubmit = project.is_submit === true;
                    project.hasRejected = project.is_reject === true;
                    project.hasPending = project.is_approve === true;
                });
            });
        });
    }

    // 初始化导航
    function initNavigation(projectsData) {

        const cities = Object.keys(projectsData);
        const cityList = document.getElementById('city-list');
        cityList.innerHTML = '';
        cities.forEach(city => {
            const cityItem = document.createElement('div');
            cityItem.className = 'city-item';

            const cityHeader = document.createElement('div');
            cityHeader.className = 'city-header';

            const cityName = document.createElement('div');
            cityName.className = 'city-name';
            cityName.textContent = city;

            // 计算城市项目统计
            const cityProjects = projectsData[city] || {};
            let totalProjects = 0;
            let completedProjects = 0;

            Object.keys(cityProjects).forEach(type => {
                totalProjects += cityProjects[type].length;
                completedProjects += cityProjects[type].filter(p => p.bao_state !== false).length;
            });

            // 创建统计徽章
            const cityStats = document.createElement('div');
            cityStats.className = 'city-stats';

            const totalBadge = document.createElement('div');
            totalBadge.className = 'stat-badge total-count';
            totalBadge.textContent = totalProjects;
            totalBadge.title = '总项目数';

            const completedBadge = document.createElement('div');
            completedBadge.className = 'stat-badge completed-count';
            completedBadge.textContent = completedProjects;
            completedBadge.title = '已完成项目数';

            cityStats.appendChild(totalBadge);
            cityStats.appendChild(completedBadge);

            cityHeader.appendChild(cityName);
            cityHeader.appendChild(cityStats);

            const optionList = document.createElement('div');
            optionList.className = 'option-list';

            // 只显示有项目的类型
            Object.keys(cityProjects).forEach(option => {
                const typeProjects = cityProjects[option];
                //if (typeProjects.length === 0) return;

                const optionItem = document.createElement('div');
                optionItem.className = 'option-item';

                const optionName = document.createElement('div');
                optionName.className = 'option-name';
                optionName.textContent = option;

                // 计算类型项目统计
                const totalTypeProjects = typeProjects.length;
                const completedTypeProjects = typeProjects.filter(p => p.bao_state !== false).length;

                // 按优先级计算状态统计，确保每个项目只计算一次
                let redCount = 0, yellowCount = 0, blueCount = 0;
                typeProjects.forEach(p => {
                    if (p.hasPendingSubmit) {
                        redCount++;
                    } else if (p.hasRejected) {
                        yellowCount++;
                    } else if (p.hasPending) {
                        blueCount++;
                    }
                });

                // 创建类型统计徽章容器
                const typeStats = document.createElement('div');
                typeStats.className = 'type-stats';

                // 添加总数量徽章
                const totalTypeBadge = document.createElement('div');
                totalTypeBadge.className = 'count-badge total-type-count';
                totalTypeBadge.textContent = totalTypeProjects;
                totalTypeBadge.title = '总项目数';
                totalTypeBadge.style.cursor = 'pointer';
                totalTypeBadge.dataset.filterType = 'all';
                typeStats.appendChild(totalTypeBadge);

                // 添加已完成数量徽章
                if (completedTypeProjects > 0) {
                    const completedTypeBadge = document.createElement('div');
                    completedTypeBadge.className = 'count-badge completed-type-count';
                    completedTypeBadge.textContent = completedTypeProjects;
                    completedTypeBadge.title = '已完成项目数';
                    completedTypeBadge.style.cursor = 'pointer';
                    completedTypeBadge.dataset.filterType = 'completed';
                    typeStats.appendChild(completedTypeBadge);

                    // 添加已完成徽章点击事件
                    completedTypeBadge.addEventListener('click', function (e) {
                        e.stopPropagation(); // 阻止冒泡，防止触发选项点击
                        filterProjectsByStatus(city, option, 'completed', '已完成');
                    });
                }

                // 添加总数量徽章点击事件（显示所有项目）
                totalTypeBadge.addEventListener('click', function (e) {
                    e.stopPropagation(); // 阻止冒泡，防止触发选项点击
                    // 清除过滤状态，显示所有项目
                    activeStatusFilter = null;
                    currentCity = city;
                    currentType = option;

                    // 清除所有城市和选项的active状态，然后设置当前选中的状态
                    document.querySelectorAll('.city-name, .option-item').forEach(el => {
                        el.classList.remove('active');
                    });

                    // 找到并激活对应的城市和选项
                    const cityElements = document.querySelectorAll('.city-name');
                    cityElements.forEach(cityEl => {
                        if (cityEl.textContent.trim() === city) {
                            cityEl.classList.add('active');
                            const cityItem = cityEl.closest('.city-item');
                            const optionElements = cityItem.querySelectorAll('.option-item');
                            optionElements.forEach(optionEl => {
                                const optionName = optionEl.querySelector('.option-name').textContent.trim();
                                if (optionName === option) {
                                    optionEl.classList.add('active');
                                }
                            });
                        }
                    });

                    loadProjects(projectsData, city, option);

                    // 移动端自动跳转到项目列表屏幕
                    if (window.innerWidth <= 768) {
                        const mobileNav = document.getElementById('mobile-nav');
                        if (mobileNav) {
                            const projectNavItem = mobileNav.querySelector('[data-screen="project-screen"]');
                            if (projectNavItem) {
                                projectNavItem.click();
                            }
                        }
                    }
                });

                if (redCount > 0) {
                    const redBadge = document.createElement('div');
                    redBadge.className = 'count-badge red-count';
                    redBadge.textContent = redCount;
                    redBadge.title = '待提交项目数';
                    redBadge.style.cursor = 'pointer';
                    redBadge.dataset.filterType = 'pending-submit';
                    typeStats.appendChild(redBadge);

                    // 添加点击事件
                    redBadge.addEventListener('click', function (e) {
                        e.stopPropagation(); // 阻止冒泡，防止触发选项点击
                        filterProjectsByStatus(city, option, 'pending-submit', '待提交');
                    });
                }

                if (yellowCount > 0) {
                    const yellowBadge = document.createElement('div');
                    yellowBadge.className = 'count-badge yellow-count';
                    yellowBadge.textContent = yellowCount;
                    yellowBadge.title = '已退回项目数';
                    yellowBadge.style.cursor = 'pointer';
                    yellowBadge.dataset.filterType = 'rejected';
                    typeStats.appendChild(yellowBadge);

                    // 添加点击事件
                    yellowBadge.addEventListener('click', function (e) {
                        e.stopPropagation(); // 阻止冒泡，防止触发选项点击
                        filterProjectsByStatus(city, option, 'rejected', '已退回');
                    });
                }

                if (blueCount > 0) {
                    const blueBadge = document.createElement('div');
                    blueBadge.className = 'count-badge blue-count';
                    blueBadge.textContent = blueCount;
                    blueBadge.title = '审核中项目数';
                    blueBadge.style.cursor = 'pointer';
                    blueBadge.dataset.filterType = 'pending';
                    typeStats.appendChild(blueBadge);

                    // 添加点击事件
                    blueBadge.addEventListener('click', function (e) {
                        e.stopPropagation(); // 阻止冒泡，防止触发选项点击
                        filterProjectsByStatus(city, option, 'pending', '审核中');
                    });
                }

                optionItem.appendChild(optionName);
                optionItem.appendChild(typeStats);

                optionItem.addEventListener('click', function () {
                    // 清除所有城市和选项的active状态
                    document.querySelectorAll('.city-name, .option-item').forEach(el => {
                        el.classList.remove('active');
                    });

                    const cityHeader = this.closest('.city-item').querySelector('.city-name');
                    cityHeader.classList.add('active');
                    // 为当前选项添加active类
                    this.classList.add('active');
                    currentCity = city;
                    currentType = option;
                    loadProjects(projectsData, city, option);

                    // 显示新增项目按钮
                    const addProjectBtn = document.getElementById('add-project-btn');
                    addProjectBtn.classList.remove('d-none');

                    // 移动端自动跳转到项目列表屏幕
                    if (window.innerWidth <= 768) {
                        const mobileNav = document.getElementById('mobile-nav');
                        if (mobileNav) {
                            // 找到项目列表导航项并触发点击
                            const projectNavItem = mobileNav.querySelector('[data-screen="project-screen"]');
                            if (projectNavItem) {
                                projectNavItem.click();
                            }
                        }
                    }
                });

                optionList.appendChild(optionItem);
            });

            cityItem.appendChild(cityHeader);
            cityItem.appendChild(optionList);
            cityList.appendChild(cityItem);

            cityName.addEventListener('click', function () {
                const optionList = this.parentElement.nextElementSibling;
                document.querySelectorAll('.city-name, .option-item').forEach(el => {
                    el.classList.remove('active');
                });
                // 为当前城市添加active类
                this.classList.add('active');

                // 隐藏新增项目按钮和搜索框
                document.getElementById('add-project-btn').classList.add('d-none');
                document.getElementById('project-search').classList.add('d-none');

                if (optionList.style.display === 'block') {
                    optionList.style.display = 'none';
                } else {
                    document.querySelectorAll('.option-list').forEach(list => {
                        list.style.display = 'none';
                    });
                    optionList.style.display = 'block';
                }
            });



        });

    }

    // 分页相关变量
    const itemsPerPage = 8;
    let currentPage = 1;
    let totalPages = 1;
    let currentCity = null;
    let currentType = null;
    let currentProjectList = [];
    let activeStatusFilter = null; // 添加状态过滤变量

    // 根据状态过滤项目列表
    function filterProjectsByStatus(city, type, statusType, statusText) {
        currentCity = city;
        currentType = type;
        activeStatusFilter = statusType;

        // 清除所有城市和选项的active状态，然后设置当前选中的状态
        document.querySelectorAll('.city-name, .option-item').forEach(el => {
            el.classList.remove('active');
        });

        // 找到并激活对应的城市和选项
        const cityElements = document.querySelectorAll('.city-name');
        cityElements.forEach(cityEl => {
            if (cityEl.textContent.trim() === city) {
                cityEl.classList.add('active');
                const cityItem = cityEl.closest('.city-item');
                const optionElements = cityItem.querySelectorAll('.option-item');
                optionElements.forEach(optionEl => {
                    const optionName = optionEl.querySelector('.option-name').textContent.trim();
                    if (optionName === type) {
                        optionEl.classList.add('active');
                    }
                });
            }
        });

        // 更新项目列表标题，显示过滤状态
        document.getElementById('project-list-title').textContent = `${city} - ${type}项目 (${statusText})`;

        // 获取完整的项目列表
        const allProjects = projectsData[city]?.[type] || [];

        // 根据状态过滤
        let filteredProjects = [];
        if (statusType === 'pending-submit') {
            filteredProjects = allProjects.filter(p => p.hasPendingSubmit);
        } else if (statusType === 'rejected') {
            filteredProjects = allProjects.filter(p => p.hasRejected);
        } else if (statusType === 'pending') {
            filteredProjects = allProjects.filter(p => p.hasPending);
        } else if (statusType === 'completed') {
            filteredProjects = allProjects.filter(p => p.bao_state !== false);
        }

        currentProjectList = filteredProjects;

        // 计算总页数
        totalPages = Math.ceil(currentProjectList.length / itemsPerPage);
        currentPage = 1;

        // 添加清除过滤按钮
        const titleElement = document.getElementById('project-list-title');
        if (!document.getElementById('clear-filter-btn')) {
            const clearFilterBtn = document.createElement('button');
            clearFilterBtn.id = 'clear-filter-btn';
            clearFilterBtn.className = 'btn btn-sm btn-outline-secondary ms-2';
            clearFilterBtn.textContent = '清除过滤';
            clearFilterBtn.style.fontSize = '12px';
            clearFilterBtn.addEventListener('click', function () {
                // 清除过滤状态
                activeStatusFilter = null;

                // 恢复完整的项目列表
                if (currentCity && currentType) {
                    currentProjectList = projectsData[currentCity][currentType] || [];
                    document.getElementById('project-list-title').textContent = `${currentCity} - ${currentType}项目`;

                    // 重置分页
                    totalPages = Math.ceil(currentProjectList.length / itemsPerPage);
                    currentPage = 1;

                    // 重新渲染
                    renderProjectList();
                    renderPagination();

                    // 移除所有激活的过滤器高亮
                    document.querySelectorAll('.count-badge').forEach(badge => {
                        badge.classList.remove('active-filter');
                    });
                }

                // 移除清除按钮
                this.remove();
            });
            titleElement.appendChild(clearFilterBtn);
        }

        renderProjectList();
        renderPagination();

        // 清空搜索框
        document.getElementById('project-search').value = '';

        // 高亮显示当前激活的过滤器
        document.querySelectorAll('.count-badge').forEach(badge => {
            badge.classList.remove('active-filter');
        });

        // 找到并高亮当前激活的过滤器
        if (activeStatusFilter) {
            document.querySelectorAll(`.count-badge[data-filter-type="${activeStatusFilter}"]`).forEach(badge => {
                badge.classList.add('active-filter');
            });
        }
    }

    // 加载项目列表
    function loadProjects(projectsData, city, type) {
        activeStatusFilter = null; // 重置状态过滤
        document.getElementById('project-list-title').textContent = `${city} - ${type}项目`;
        currentProjectList = projectsData[city]?.[type] || [];
        if (currentProjectList.length === 0) {
            document.getElementById('project-list').innerHTML = '<div class="no-selection">暂无项目数据</div>';
            document.getElementById('review-list').innerHTML = '<div class="no-selection">请从左侧选择一个项目</div>';
            document.getElementById('pagination').innerHTML = '';

            // 重置审核状态标题
            const reviewTitle = document.querySelector('#review-screen h3');
            if (reviewTitle) {
                reviewTitle.textContent = '审核状态';
            }

            // 即使没有数据，也要显示搜索框（用户可能想搜索或添加新项目）
            const searchInput = document.getElementById('project-search');
            searchInput.classList.remove('d-none');
            searchInput.value = '';

            return;
        }



        // 计算总页数
        totalPages = Math.ceil(currentProjectList.length / itemsPerPage);
        currentPage = 1;

        renderProjectList();
        renderPagination();

        // 显示搜索框（选择地市类型后）
        const searchInput = document.getElementById('project-search');
        searchInput.classList.remove('d-none');

        // 清空搜索框
        searchInput.value = '';
    }

    // 渲染项目列表
    function renderProjectList() {
        const projectListContainer = document.getElementById('project-list');
        projectListContainer.innerHTML = '';

        if (currentProjectList.length === 0) {
            projectListContainer.innerHTML = '<div class="no-selection">没有找到匹配的项目</div>';
            document.getElementById('review-list').innerHTML = '<div class="no-selection">请从左侧选择一个项目</div>';

            // 重置审核状态标题
            const reviewTitle = document.querySelector('#review-screen h3');
            if (reviewTitle) {
                reviewTitle.textContent = '审核状态';
            }

            return;
        }

        // 计算当前页的项目
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = Math.min(startIndex + itemsPerPage, currentProjectList.length);
        const currentPageProjects = currentProjectList.slice(startIndex, endIndex);

        currentPageProjects.forEach(project => {
            const projectItem = document.createElement('div');
            projectItem.className = 'project-item';
            projectItem.dataset.id = project.id;

            // 确定状态类名和颜色
            let statusClass = '';
            let leftBarColor = '#ccc'; // 默认颜色
            let statusText = project.bao_state === false ? "进行中" : "已完成";

            if (project.hasPendingSubmit) {
                statusClass = 'status-pending-submit';
                leftBarColor = '#e74c3c';
                statusText = "待提交";
            } else if (project.hasRejected) {
                statusClass = 'status-rejected';
                leftBarColor = '#f1c40f';
                statusText = "已退回";
            } else if (project.hasPending) {
                statusClass = 'status-pending';
                leftBarColor = '#3498db';
                statusText = "待审核";
            }

            // 设置左侧竖条的颜色（仅使用CSS变量方式）
            projectItem.style.setProperty('--left-bar-color', leftBarColor);

            projectItem.innerHTML = `
                <div class="project-name">${project.bao_name}</div>
                <div class="project-meta">
                    <span>工单编号：${project.bao_bm}</span>
                </div>
                 <div class="project-meta">
                    <span>用户户号：${project.usercode || 'N/A'}</span>
                </div>
                 <div class="project-meta">
                    <span>供电所：${project.gds || 'N/A'}</span>
                </div>
                <span class="project-status ${statusClass}">${statusText}</span>
            `;

            projectItem.addEventListener('click', function () {
                document.querySelectorAll('.project-item').forEach(item => {
                    item.classList.remove('active');
                });
                this.classList.add('active');
                loadProjectReview(project);
                loadProjectDetails(project);

                // 移动端自动跳转到审核状态屏幕
                if (window.innerWidth <= 768) {
                    const mobileNav = document.getElementById('mobile-nav');
                    if (mobileNav) {
                        // 找到审核状态导航项并触发点击
                        const reviewNavItem = mobileNav.querySelector('[data-screen="review-screen"]');
                        if (reviewNavItem) {
                            reviewNavItem.click();
                        }
                    }
                }
            });


            projectListContainer.appendChild(projectItem);
        });
    }

    // 渲染分页控件
    function renderPagination() {
        const paginationContainer = document.getElementById('pagination');
        if (!paginationContainer) return;

        paginationContainer.innerHTML = '';

        if (totalPages <= 1) {
            paginationContainer.style.display = 'none';
            return;
        }

        paginationContainer.style.display = 'flex';

        // 上一页按钮
        const prevBtn = document.createElement('button');
        prevBtn.className = 'page-btn page-nav-btn';
        prevBtn.innerHTML = '&lt;';
        prevBtn.title = '上一页';
        prevBtn.disabled = currentPage === 1;
        if (currentPage === 1) {
            prevBtn.classList.add('disabled');
        }
        prevBtn.addEventListener('click', function () {
            if (currentPage > 1) {
                currentPage--;
                renderProjectList();
                renderPagination();
            }
        });
        paginationContainer.appendChild(prevBtn);

        // 简化的分页逻辑 - 只显示第一页和最后一页
        if (totalPages <= 3) {
            // 总页数较少时，显示所有页码
            for (let i = 1; i <= totalPages; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `page-btn ${i === currentPage ? 'active' : ''}`;
                pageBtn.textContent = i;
                pageBtn.addEventListener('click', function () {
                    currentPage = i;
                    renderProjectList();
                    renderPagination();
                });
                paginationContainer.appendChild(pageBtn);
            }
        } else {
            // 总页数较多时，只显示第一页、当前页和最后一页

            // 显示第一页
            const firstPageBtn = document.createElement('button');
            firstPageBtn.className = `page-btn ${currentPage === 1 ? 'active' : ''}`;
            firstPageBtn.textContent = '1';
            firstPageBtn.addEventListener('click', function () {
                currentPage = 1;
                renderProjectList();
                renderPagination();
            });
            paginationContainer.appendChild(firstPageBtn);

            // 如果当前页不是第一页和最后一页，则显示当前页
            if (currentPage !== 1 && currentPage !== totalPages) {
                // 省略号
                const ellipsis1 = document.createElement('span');
                ellipsis1.className = 'page-ellipsis';
                ellipsis1.textContent = '...';
                paginationContainer.appendChild(ellipsis1);

                // 当前页
                const currentPageBtn = document.createElement('button');
                currentPageBtn.className = 'page-btn active';
                currentPageBtn.textContent = currentPage;
                paginationContainer.appendChild(currentPageBtn);

                // 省略号
                const ellipsis2 = document.createElement('span');
                ellipsis2.className = 'page-ellipsis';
                ellipsis2.textContent = '...';
                paginationContainer.appendChild(ellipsis2);
            } else if (currentPage === 1 && totalPages > 2) {
                // 当前页是第一页，只显示一个省略号
                const ellipsis = document.createElement('span');
                ellipsis.className = 'page-ellipsis';
                ellipsis.textContent = '...';
                paginationContainer.appendChild(ellipsis);
            } else if (currentPage === totalPages && totalPages > 2) {
                // 当前页是最后一页，只显示一个省略号
                const ellipsis = document.createElement('span');
                ellipsis.className = 'page-ellipsis';
                ellipsis.textContent = '...';
                paginationContainer.appendChild(ellipsis);
            }

            // 显示最后一页
            const lastPageBtn = document.createElement('button');
            lastPageBtn.className = `page-btn ${currentPage === totalPages ? 'active' : ''}`;
            lastPageBtn.textContent = totalPages;
            lastPageBtn.addEventListener('click', function () {
                currentPage = totalPages;
                renderProjectList();
                renderPagination();
            });
            paginationContainer.appendChild(lastPageBtn);
        }

        // 下一页按钮
        const nextBtn = document.createElement('button');
        nextBtn.className = 'page-btn page-nav-btn';
        nextBtn.innerHTML = '&gt;';
        nextBtn.title = '下一页';
        nextBtn.disabled = currentPage === totalPages;
        if (currentPage === totalPages) {
            nextBtn.classList.add('disabled');
        }
        nextBtn.addEventListener('click', function () {
            if (currentPage < totalPages) {
                currentPage++;
                renderProjectList();
                renderPagination();
            }
        });
        paginationContainer.appendChild(nextBtn);

        // 页面跳转功能
        const pageJump = document.createElement('div');
        pageJump.className = 'page-jump';

        const pageJumpInput = document.createElement('input');
        pageJumpInput.type = 'number';
        pageJumpInput.min = 1;
        pageJumpInput.max = totalPages;
        pageJumpInput.value = currentPage;
        pageJump.appendChild(pageJumpInput);

        const pageJumpBtn = document.createElement('button');
        pageJumpBtn.textContent = '跳转';
        pageJumpBtn.addEventListener('click', function () {
            let pageNum = parseInt(pageJumpInput.value);
            if (isNaN(pageNum)) pageNum = 1;
            if (pageNum < 1) pageNum = 1;
            if (pageNum > totalPages) pageNum = totalPages;

            if (pageNum !== currentPage) {
                currentPage = pageNum;
                renderProjectList();
                renderPagination();
            }
        });
        pageJump.appendChild(pageJumpBtn);

        // 回车键支持
        pageJumpInput.addEventListener('keypress', function (e) {
            if (e.key === 'Enter') {
                pageJumpBtn.click();
            }
        });

        paginationContainer.appendChild(pageJump);
    }

    // 加载审核状态
    function loadProjectReview(project) {
        const files = project.filesTemplate || [];
        const reviewListContainer = document.getElementById('review-list');
        reviewListContainer.innerHTML = '';
        currentSelectedProject = project;

        // 更新审核状态屏幕的标题，显示项目名称
        const reviewTitle = document.querySelector('#review-screen h3');
        if (reviewTitle) {
            reviewTitle.textContent = `审核状态 - ${project.bao_name}`;
        }

        if (files.length === 0) {
            reviewListContainer.innerHTML = '<div class="no-selection">暂无文件数据</div>';
            return;
        }

        let file_index = 0;
        files.forEach(file => {
            file_index++;
            const reviewItem = document.createElement('div');
            reviewItem.className = 'review-item';
            reviewItem.dataset.fileId = file.file_id;

            // 查找该文件是否有上传记录
            let uploadedFile = null;
            if (project.files && Array.isArray(project.files)) {
                uploadedFile = project.files.find(f => f.file_lx == file.file_id);
            }

            // 默认状态
            let statusClass = file.status;
            let statusText = statusTextMap[file.status] || file.status;
            let reason = file.reason || '';
            let fileName = '';
            let fileHash = '';

            // 如果存在上传记录，使用上传记录中的状态
            if (uploadedFile) {
                // 将数字状态转换为字符串状态
                const stateMap = {
                    0: 'pending',     // 审核中
                    1: 'approved',    // 已通过
                    2: 'rejected',    // 已退回
                    3: 'pending-submit' // 待提交
                };

                statusClass = stateMap[uploadedFile.file_state] || file.status;
                statusText = statusTextMap[statusClass] || statusClass;
                reason = uploadedFile.file_bz || file.reason || '';
                fileName = uploadedFile.file_name || '';
                fileHash = uploadedFile.file_hash || '';
            }

            reviewItem.innerHTML = `
                <div class="review-header">
                    <div class="file-name">${file_index}. ${file.name}</div>
                    <div class="file-status ${statusClass}">${statusText}</div>
                </div>
                ${reason ? `<div class="reason-box">${reason}</div>` : '<div class="reason-box hidden"></div>'}
                <div class="file-actions">
                    <button class="download-btn" data-file-id="${file.id || ''}" data-file-hash="${fileHash}">查看文件</button>
                    <button class="upload-btn ${statusClass === 'rejected' || statusClass === 'pending-submit' ? '' : 'hidden'}">${statusClass === 'pending-submit' ? '本地上传' : '重新上传'}</button>
                    <button class="upload-gp-btn ${statusClass === 'rejected' || statusClass === 'pending-submit' ? '' : 'hidden'}">${statusClass === 'pending-submit' ? '高拍上传' : '高拍重新上传'}</button>
                    <button class="pending-btn ${statusClass === 'pending' ? '' : 'hidden'}">审核文件</button>
                    ${fileName ? `<div class="file-name">已上传: ${fileName}</div>` : ''}
                </div>
                <div class="file-preview" id="preview-${file.file_id}"></div>
            `;

            reviewListContainer.appendChild(reviewItem);
        });

        bindButtonEvents();
    }

    // 加载项目详情
    function loadProjectDetails(project) {



        const userinfoListContainer = document.getElementById('userinfo-list');
        userinfoListContainer.innerHTML = '';
        // 项目详情显示逻辑

        // -- NEW UI GENERATION --
        let projectDetailsHTML = '';

        // 生成模拟用户数据和真实工单数据
        const mockUser = generateMockUserData(project.usercode);

        // 调试：打印项目数据


        const realGongdan = generateRealGongdanData(project);

        // 1. 用户信息卡片
        if (mockUser) {
            projectDetailsHTML += `
                <div class="card mb-3 user-details">
                    <div class="card-header bg-white">
                        <h5 class="mb-0"><i class="bi bi-person-badge-fill me-2 text-primary"></i>用户信息（模拟数据）</h5>
                    </div>
                    <div class="card-body">
                        <dl class="row g-2">
                            <dt class="col-sm-4"><i class="bi bi-person me-2"></i>姓名</dt>
                            <dd class="col-sm-8">${mockUser.name || '未录入'}</dd>

                            <dt class="col-sm-4"><i class="bi bi-telephone me-2"></i>电话</dt>
                            <dd class="col-sm-8">${mockUser.phone || '未录入'}</dd>

                            <dt class="col-sm-4"><i class="bi bi-hash me-2"></i>户号</dt>
                            <dd class="col-sm-8">${mockUser.usercode || '未录入'}</dd>

                            <dt class="col-sm-4"><i class="bi bi-geo-alt me-2"></i>地址</dt>
                            <dd class="col-sm-8">${mockUser.address || '未录入'}</dd>

                            <dt class="col-sm-4"><i class="bi bi-person-vcard me-2"></i>身份证</dt>
                            <dd class="col-sm-8">${mockUser.id_card || '未录入'}</dd>

                            <dt class="col-sm-4"><i class="bi bi-bank me-2"></i>开户行</dt>
                            <dd class="col-sm-8">${mockUser.bank_name || '未录入'}</dd>

                            <dt class="col-sm-4"><i class="bi bi-credit-card me-2"></i>银行卡</dt>
                            <dd class="col-sm-8">${mockUser.bank_card || '未录入'}</dd>
                        </dl>
                    </div>
                </div>
            `;
        } else {
            projectDetailsHTML += '<div class="card mb-3 user-details"><div class="card-body"><div class="no-selection">暂无用户数据</div></div></div>';
        }

        // 2. 工单流程信息卡片
        projectDetailsHTML += '<div class="card mb-3">';
        projectDetailsHTML += `
            <div class="card-header bg-white">
                 <h5 class="mb-0"><i class="bi bi-kanban-fill me-2 text-primary"></i>工单流程</h5>
                 ${project.gdlx ? `<small class="text-muted">工单类型：${project.gdlx}</small>` : ''}
                 ${project.htrl ? `<small class="text-muted ms-3">合同容量：${project.htrl} kVA</small>` : ''}
            </div>`;

        if (realGongdan && Array.isArray(realGongdan) && realGongdan.length > 0) {
            let gongdanTimelineHTML = '';
            realGongdan.forEach(item => {
                const isCompleted = item.statusClass === 'completed';
                const isNotEntered = item.statusClass === 'not-entered';
                const statusClass = isCompleted ? 'completed' : (isNotEntered ? 'not-entered' : 'pending');

                let statusIcon, badgeClass, badgeText;
                if (isCompleted) {
                    statusIcon = '<i class="bi bi-check-lg"></i>';
                    badgeClass = 'bg-success';
                    badgeText = '已完成';
                } else if (isNotEntered) {
                    statusIcon = '<i class="bi bi-exclamation-triangle"></i>';
                    badgeClass = 'bg-secondary';
                    badgeText = '数据未录入';
                } else {
                    statusIcon = '<i class="bi bi-hourglass-split"></i>';
                    badgeClass = 'bg-warning';
                    badgeText = '进行中';
                }

                gongdanTimelineHTML += `
                    <li class="timeline-item ${statusClass}">
                        <div class="timeline-icon">${statusIcon}</div>
                        <div class="timeline-content">
                            <h6>
                                ${item.stepName}
                                <span class="badge ${badgeClass} ms-2">${badgeText}</span>
                            </h6>
                            <p class="text-muted mb-0">
                                ${isNotEntered ?
                        '<span></span>' :
                        `${item.timeDisplay}`
                    }
                            </p>
                        </div>
                    </li>
                `;
            });

            projectDetailsHTML += `
                <div class="card-body">
                    <ul class="timeline">
                        ${gongdanTimelineHTML}
                    </ul>
                </div>
            `;
        } else {
            projectDetailsHTML += '<div class="card-body"><div class="no-selection">暂无工单数据</div></div>';
        }
        projectDetailsHTML += '</div>';

        userinfoListContainer.innerHTML = projectDetailsHTML;
    }
    // 绑定按钮事件
    function bindButtonEvents() {
        // 上传按钮事件
        document.querySelectorAll('.upload-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                const fileId = this.closest('.review-item').dataset.fileId;
                const fileElement = this.closest('.review-item');

                // 创建文件输入框
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.accept = '.pdf';
                fileInput.style.display = 'none';

                fileInput.addEventListener('change', function (e) {
                    if (this.files && this.files[0]) {
                        const file = this.files[0];
                        uploadFile(currentSelectedProject.id, fileId, file, fileElement);
                    }
                });

                document.body.appendChild(fileInput);
                fileInput.click();
                document.body.removeChild(fileInput);
            });
        });
        //高拍上传按钮事件
        document.querySelectorAll('.upload-gp-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                if (isGpUploading) {
                    alert('已有文件正在高拍上传中，请稍后再试');
                    return;
                }
                isGpUploading = true;
                window.fileElement = this.closest('.review-item');
                window.file_id = this.closest('.review-item').dataset.fileId;
                window.bao_id = currentSelectedProject.id;

                // 获取完整的文件名信息（如 "1. 申请表"）
                const fileNameElem = this.closest('.review-item').querySelector('.file-name');
                window.file_name = fileNameElem.textContent;

                // 提取模板文件名（去掉序号和点）
                let templateFileName = '';
                const match = window.file_name.match(/^\d+\.\s+(.*?)$/);
                if (match && match[1]) {
                    templateFileName = match[1].trim();
                } else {
                    templateFileName = '文件_' + window.file_id;
                }

                // 保存模板文件名供后面使用
                window.templateFileName = templateFileName + '.pdf';

                if (window.WebSocketFlag == false) {
                    alert("高拍仪服务未连接,无法使用高拍上传");
                    isGpUploading = false;
                    return;
                }
                window.socket.send(JSON.stringify({
                    'FuncName': 'StartPreview',
                    'CamIndex': 0
                }));
                // 创建文件输入框
                const modalHTML = `
    <div class="modal fade" id="addProjectModal1" tabindex="-1" aria-labelledby="addProjectModalLabel1" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addProjectModalLabel1">高拍上传</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <img id="myCanvas" width='640' height='480' style="background-color: black; float: left;" />
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="confirmUpload">确认上传</button>
                    <button type="button" class="btn btn-secondary" id="paizhao">拍照</button>
                </div>
            </div>
        </div>
    </div>
`;
                // 新增项目添加模态框到DOM
                document.body.insertAdjacentHTML('beforeend', modalHTML);

                // 初始化模态框
                const modal = new bootstrap.Modal(document.getElementById('addProjectModal1'));
                // 初始化模态框
                modal.show();

                // 隐藏模态框时移除元素

                let uploadConfirmed = false;
                window.paizhao_index = 0;
                document.getElementById('paizhao').addEventListener('click', function () {
                    // 获取当前的 canvas 元素

                    const canvas = document.getElementById('myCanvas');

                    // 将 canvas 内容转换为图像数据 URL
                    const imgDataUrl = canvas.src;
                    if (imgDataUrl == "") {
                        alert("高拍仪无数据,请检查是否连接");
                        return;
                    }
                    window.is_pz = true;
                    // 创建一个新的 img 元素用于缩略图
                    const thumbnail = document.createElement('img');
                    thumbnail.src = imgDataUrl;
                    thumbnail.width = 100; // 缩略图宽度
                    thumbnail.height = 100; // 缩略图高度
                    thumbnail.style.margin = '5px'; // 添加一些间距

                    // 获取 modal-body 元素
                    const modalBody = document.querySelector('#addProjectModal1 .modal-body');

                    // 将新创建的缩略图添加到 modal-body 中
                    modalBody.appendChild(thumbnail);
                    socket.send(JSON.stringify({
                        'FuncName': 'SaveToImage',
                        'FileName': 'D:\\saveimg' + window.paizhao_index + '.jpg'//linux : /tmp/111.jpg
                    }));
                    window.paizhao_index++;
                });
                document.getElementById('confirmUpload').addEventListener('click', function () {//确认上传

                    if (window.is_pz) {
                        uploadConfirmed = true;
                        window.is_pz = false;
                        var str = '';
                        for (var i = 0; i < window.paizhao_index; i++) {
                            str = str + 'D:\\saveimg' + i + '.jpg@'
                        }
                        socket.send(JSON.stringify({
                            'FuncName': 'SaveToPDF',
                            'PDFPath': 'D:\\savepdf.pdf',
                            'ImagePathList': str,
                            'DeleteImg': 1
                        }));
                        modal.hide();
                    } else {
                        alert("请先拍照");
                    }

                });

                document.getElementById('addProjectModal1').addEventListener('hidden.bs.modal', function () {
                    if (!uploadConfirmed) {
                        isGpUploading = false; // 用户取消，重置标志
                    }
                    window.is_pz = false;
                    this.remove();
                });


            });
        });

        //点击每一项文件加底图颜色
        const reviewItems = document.querySelectorAll('.review-item');
        reviewItems.forEach(item => {
            item.addEventListener('click', function () {
                // 先清除所有项的样式
                reviewItems.forEach(i => {
                    i.style.background = '';
                });

                // 为当前点击的项设置背景色
                this.style.background = '#00706b';
            });
        });
        // 下载按钮事件
        document.querySelectorAll('.download-btn').forEach(btn => {
            btn.addEventListener('click', function () {

                const projectId = currentSelectedProject.id;
                const fileId = this.closest('.review-item').dataset.fileId;
                const fileHash = this.dataset.fileHash;
                const reviewItem = this.closest('.review-item')
                const reviewItemStatus = reviewItem.querySelector('.file-status');

                // 检查下载状态
                if (isDownloading) {
                    const isMobile = window.innerWidth <= 768;
                    if (isMobile) {
                        showNotification("有文件正在下载中，请稍后再试", true);
                    } else {
                        alert("有文件正在下载中，请稍后再试");
                    }
                    return;
                }

                //reviewItem.style="background:#bb3d8d"
                if (reviewItemStatus.textContent == "待提交") {
                    const isMobile = window.innerWidth <= 768;
                    if (isMobile) {
                        showNotification('该文件尚未上传，无法查看', true);
                    } else {
                        alert('该文件尚未上传，无法下载');
                    }
                    return;
                }

                // 移动端显示加载提示
                if (window.innerWidth <= 768) {
                    showNotification('正在准备文件，请稍候...');
                }

                downloadFile(projectId, fileId);

            });
        });

        // 审核按钮事件
        document.querySelectorAll('.pending-btn').forEach(btn => {
            btn.addEventListener('click', function (event) {
                const projectId = currentSelectedProject.id;
                const fileId = this.closest('.review-item').dataset.fileId;
                //file_id转成数字
                const file_lx = fileId;
                const fileName = this.closest('.review-item').querySelector('.file-name').textContent;
                const reviewItem = this.closest('.review-item');
                const buttonRect = event.currentTarget.getBoundingClientRect();


                // 创建审核模态框
                const modalHTML = `
<div class="modal fade" id="review-modal" tabindex="-1" aria-labelledby="reviewModalLabel" aria-hidden="true">
<div class="modal-dialog">
<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">审核文件</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
    </div>
    <div class="modal-body">
        <p>${fileName}</p>
        <div id="reject-reason-container" style="display: none; margin-top: 15px;">
            <textarea id="reject-reason" class="form-control" placeholder="请输入退回原因..." rows="3"></textarea>
            <button id="submit-reject-btn" type="button" class="btn btn-danger">确认退回</button>
            <button id="cancel-reject-btn" type="button" class="btn btn-secondary">取消</button>
        </div>
    </div>
    <div class="modal-footer">
        <button id="approve-btn" type="button" class="btn btn-primary">通过</button>
        <button id="reject-btn" type="button" class="btn btn-danger">退回</button>
        <button id="cancel-btn" type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
    </div>
</div>
</div>
</div>
`;

                // 添加模态框到DOM
                document.body.insertAdjacentHTML('beforeend', modalHTML);
                const modal = document.getElementById('review-modal');
                // Position modal before showing
                const modalDialog = modal.querySelector('.modal-dialog');
                if (modalDialog) {
                    // Temporarily make it visible to measure
                    modal.style.display = 'block';
                    modal.style.visibility = 'hidden';
                    const modalWidth = modalDialog.offsetWidth;
                    const modalHeight = modalDialog.offsetHeight;
                    modal.style.display = '';
                    modal.style.visibility = '';

                    // Calculate position
                    let top = buttonRect.top - modalHeight - 5; // Position above the button
                    let left = buttonRect.left;

                    // Adjust if it goes off-screen
                    if (left + modalWidth > window.innerWidth) {
                        left = window.innerWidth - modalWidth - 10; // 10px margin
                    }
                    if (top + modalHeight > window.innerHeight) {
                        top = window.innerHeight - modalHeight - 10; // 10px margin
                    }
                    if (left < 0) left = 10;
                    if (top < 0) top = 10;

                    // Apply position
                    modalDialog.style.position = 'fixed';
                    modalDialog.style.margin = '0';
                    modalDialog.style.top = `${top}px`;
                    modalDialog.style.left = `${left}px`;
                }

                // 初始化模态框
                const modalInstance = new bootstrap.Modal(modal, {
                    keyboard: false
                });
                // 显示模态框
                modalInstance.show();
                // 通过按钮事件
                document.getElementById('approve-btn').addEventListener('click', function () {
                    // 移除模态框
                    modalInstance.hide();
                    // 发送API请求
                    fetch(`${API_BASE_URL}/file_pending`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            bao_id: projectId,
                            file_lx: parseInt(file_lx),
                            state: 1,
                            reason: null
                        })
                    })
                        .then(response => response.json())
                        .then(data => {
                            if (data.msg != "操作成功" && data.msg != "审核完毕") {
                                showNotification('文件审核失败: ' + data.msg, true);
                            } else {
                                if (data.msg == "审核完毕") {
                                    alert("工单文件全部通过，流程已经结束，可以刷新页面显示最新数据。");
                                }
                                showNotification(`文件 "${fileName}" 已审核通过`);

                                // 更新UI显示
                                const fileStatusEl = reviewItem.querySelector('.file-status');
                                if (fileStatusEl) {
                                    fileStatusEl.textContent = '已通过';
                                    fileStatusEl.className = 'file-status approved';
                                }
                                btn.classList.add('hidden');

                                // 统一更新本地数据和UI
                                updateProjectFileStatus(projectId, file_lx, 1, null, null, null, false);
                            }
                        })
                        .catch(error => {
                            showNotification('文件审核失败，请重试', true);
                        });
                });

                // 退回按钮事件
                document.getElementById('reject-btn').addEventListener('click', function () {
                    // 显示退回原因输入框
                    document.getElementById('reject-reason-container').style.display = 'block';
                    document.getElementById('approve-btn').style.display = 'none';
                    document.getElementById('reject-btn').style.display = 'none';
                    document.getElementById('cancel-btn').style.display = 'none';
                });


                // 确认退回按钮事件
                document.getElementById('submit-reject-btn').addEventListener('click', function () {
                    const reason = document.getElementById('reject-reason').value.trim();
                    if (reason) {
                        // 移除模态框
                        modalInstance.hide();
                        // 发送API请求
                        fetch(`${API_BASE_URL}/file_pending`, {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                bao_id: projectId,
                                file_lx: parseInt(file_lx),
                                state: 2,
                                reason: reason
                            })
                        })
                            .then(response => response.json())
                            .then(data => {
                                if (data.msg != "操作成功") {
                                    showNotification('文件退回失败: ' + data.msg, true);
                                } else {
                                    showNotification(`文件 "${fileName}" 已退回`);

                                    // 更新UI显示
                                    const fileStatusEl = reviewItem.querySelector('.file-status');
                                    if (fileStatusEl) {
                                        fileStatusEl.textContent = '已退回';
                                        fileStatusEl.className = 'file-status rejected';
                                    }

                                    btn.classList.add('hidden');

                                    // 显示上传按钮
                                    const uploadBtn = reviewItem.querySelector('.upload-btn');
                                    const uploadGpBtn = reviewItem.querySelector('.upload-gp-btn');
                                    if (uploadBtn) uploadBtn.classList.remove('hidden');
                                    if (uploadGpBtn) uploadGpBtn.classList.remove('hidden');

                                    // 显示退回原因
                                    const reasonBox = reviewItem.querySelector('.reason-box');
                                    if (reasonBox) {
                                        reasonBox.textContent = reason;
                                        reasonBox.classList.remove('hidden');
                                    }

                                    // 统一更新本地数据和UI
                                    updateProjectFileStatus(projectId, file_lx, 2, reason, null, null, false);
                                }
                            })
                            .catch(error => {
                                showNotification('文件退回失败，请重试', true);
                            });
                    } else {
                        alert('请输入退回原因');
                    }
                });

                // 取消退回按钮事件
                document.getElementById('cancel-reject-btn').addEventListener('click', function () {
                    document.getElementById('reject-reason-container').style.display = 'none';
                    document.getElementById('approve-btn').style.display = 'block';
                    document.getElementById('reject-btn').style.display = 'block';
                    document.getElementById('cancel-btn').style.display = 'block';
                });

                // 取消按钮事件
                document.getElementById('cancel-btn').addEventListener('click', function () {
                    modalInstance.hide();
                });
                document.getElementById('review-modal').addEventListener('hidden.bs.modal', function () {
                    const approveBtn = document.getElementById('approve-btn');
                    if (approveBtn) approveBtn.replaceWith(approveBtn.cloneNode(true));

                    const rejectBtn = document.getElementById('reject-btn');
                    if (rejectBtn) rejectBtn.replaceWith(rejectBtn.cloneNode(true));

                    const submitRejectBtn = document.getElementById('submit-reject-btn');
                    if (submitRejectBtn) submitRejectBtn.replaceWith(submitRejectBtn.cloneNode(true));

                    this.remove();
                });
            });
        });
    }
    /**
     * 更新项目文件状态并同步UI显示
     * @param {number} projectId - 项目ID
     * @param {number} fileType - 文件类型ID
     * @param {number} newStatus - 新状态 (0-待审核, 1-已通过, 2-已退回)
     * @param {string} reason - 退回原因
     * @param {string} file_name - 文件名
     * @param {string} file_hash - 文件哈希值
     * @param {boolean} skipUIUpdate - 是否跳过UI更新（用于批量操作）
     */
    function updateProjectFileStatus(projectId, fileType, newStatus, reason = null, file_name = null, file_hash = null, skipUIUpdate = false) {
        let updated = false;
        const currentTimestamp = Math.floor(Date.now() / 1000);

        // 保存当前展开的城市和选中的类型
        const expandedCities = [];
        const selectedCity = currentCity;
        const selectedType = currentType;

        if (!skipUIUpdate) {
            document.querySelectorAll('.option-list').forEach((list, index) => {
                if (list.style.display === 'block') {
                    const cityItem = list.closest('.city-item');
                    const cityName = cityItem.querySelector('.city-name').textContent.trim();
                    expandedCities.push(cityName);
                }
            });
        }

        // 保存当前选中的城市和类型
        const activeCityEl = document.querySelector('.city-name.active');
        const activeTypeEl = document.querySelector('.option-item.active');
        const activeCity = activeCityEl ? activeCityEl.textContent.trim() : null;
        const activeType = activeTypeEl ? activeTypeEl.querySelector('div').textContent.trim() : null;

        // 遍历projectsData找到对应的项目和文件
        for (const city in projectsData) {
            for (const type in projectsData[city]) {
                const projects = projectsData[city][type];
                const projectIndex = projects.findIndex(p => p.id === projectId);
                if (projectIndex !== -1) {
                    const project = projects[projectIndex];

                    // 确保files数组存在
                    if (!project.files) {
                        project.files = [];
                    }

                    // 查找或创建对应的文件记录
                    let fileIndex = project.files.findIndex(f => f.file_lx == fileType);
                    if (fileIndex === -1) {
                        // 如果不存在，创建新记录
                        const newFile = {
                            file_lx: parseInt(fileType),
                            file_state: newStatus,
                            file_bz: reason || '',
                            file_name: file_name || '',
                            file_hash: file_hash || '',
                            file_start: currentTimestamp,
                            file_end: newStatus === 1 ? currentTimestamp : ''
                        };
                        project.files.push(newFile);
                        fileIndex = project.files.length - 1;
                    } else {
                        // 更新现有记录
                        const existingFile = project.files[fileIndex];
                        existingFile.file_state = newStatus;

                        if (reason !== undefined) {
                            existingFile.file_bz = reason;
                        }
                        if (file_name) {
                            existingFile.file_name = file_name;
                        }
                        if (file_hash !== null) {
                            existingFile.file_hash = file_hash;
                        }

                        // 更新时间戳
                        if (newStatus === 1) { // 审核通过
                            existingFile.file_end = currentTimestamp;
                        } else if (newStatus === 2) { // 退回
                            existingFile.file_end = '';
                        }
                    }

                    // 重新计算项目状态标识
                    updateProjectStatusFlags(project);

                    // 同步更新当前选中的项目对象
                    if (currentSelectedProject && currentSelectedProject.id === projectId) {
                        currentSelectedProject = project;
                    }

                    updated = true;

                    // 智能UI更新：只在必要时更新UI
                    if (!skipUIUpdate) {
                        // 更新导航栏统计数据
                        processData(projectsData);
                        initNavigation(projectsData);

                        // 恢复导航栏的展开状态
                        setTimeout(() => {
                            // 恢复展开的城市
                            expandedCities.forEach(cityName => {
                                const cityElements = document.querySelectorAll('.city-name');
                                cityElements.forEach(element => {
                                    if (element.textContent.trim() === cityName) {
                                        const optionList = element.parentElement.nextElementSibling;
                                        optionList.style.display = 'block';
                                    }
                                });
                            });

                            // 恢复选中的城市和类型
                            if (activeCity && activeType) {
                                const cityElements = document.querySelectorAll('.city-name');
                                cityElements.forEach(element => {
                                    if (element.textContent.trim() === activeCity) {
                                        element.classList.add('active');
                                        const cityItem = element.closest('.city-item');
                                        const typeElements = cityItem.querySelectorAll('.option-item');

                                        typeElements.forEach(typeElement => {
                                            const typeName = typeElement.querySelector('div').textContent.trim();
                                            if (typeName === activeType) {
                                                typeElement.classList.add('active');
                                            }
                                        });
                                    }
                                });
                            }
                        }, 0);
                    }

                    // 实时更新项目列表中的状态显示
                    updateProjectListItemStatus(projectId, project);

                    // 如果是当前选中的项目，实时更新详情页面
                    if (currentSelectedProject && currentSelectedProject.id === projectId) {
                        // 更新用户详情页面
                        loadProjectDetails(project);
                        // 更新审核页面
                        loadProjectReview(project);
                    }

                    // 跳出循环，因为已找到并更新了项目
                    return true;
                }
            }
        }

        return updated;
    }

    /**
     * 重新计算项目状态标识
     * @param {Object} project - 项目对象
     */
    function updateProjectStatusFlags(project) {
        if (!project.files || project.files.length === 0) {
            project.hasPendingSubmit = true; // 没有文件时为待提交状态
            project.hasRejected = false;
            project.hasPending = false;
            return;
        }

        // 重新计算状态标识
        project.hasPendingSubmit = false;
        project.hasRejected = false;
        project.hasPending = false;

        // 检查是否有待提交的文件（根据项目类型的文件模板数量判断）
        const template = fileTemplates[project.bao_lx] || [];
        const requiredFileCount = template.length;
        const uploadedFileCount = project.files.length;

        if (uploadedFileCount < requiredFileCount) {
            project.hasPendingSubmit = true;
        }

        // 检查文件状态
        project.files.forEach(file => {
            switch (file.file_state) {
                case 0: // 待审核
                    project.hasPending = true;
                    break;
                case 2: // 已退回
                    project.hasRejected = true;
                    break;
                // case 1: 已通过，不需要特殊处理
            }
        });

        // 更新项目整体状态
        if (project.files.length >= requiredFileCount &&
            project.files.every(f => f.file_state === 1)) {
            // 所有文件都已通过审核
            project.bao_state = true;
        }
    }

    /**
     * 更新项目列表中单个项目的状态显示
     * @param {number} projectId - 项目ID
     * @param {Object} project - 项目对象
     */
    function updateProjectListItemStatus(projectId, project) {
        const projectElement = document.querySelector(`div[data-id="${projectId}"]`);
        if (!projectElement) return;

        // 更新左侧竖条颜色和状态文本
        let leftBarColor = '#ccc';
        let statusText = project.bao_state === false ? "进行中" : "已完成";

        if (project.hasPendingSubmit) {
            leftBarColor = '#e74c3c';
            statusText = "待提交";
        } else if (project.hasRejected) {
            leftBarColor = '#f1c40f';
            statusText = "已退回";
        } else if (project.hasPending) {
            leftBarColor = '#3498db';
            statusText = "待审核";
        }

        // 更新左侧竖条颜色
        projectElement.style.setProperty('--left-bar-color', leftBarColor);

        // 更新状态文本
        const statusElement = projectElement.querySelector('.project-status');
        if (statusElement) {
            statusElement.textContent = statusText;
        }

        // 更新项目元素的CSS类
        projectElement.className = 'project-item';
        if (project.hasPendingSubmit) {
            projectElement.classList.add('status-pending-submit');
        } else if (project.hasRejected) {
            projectElement.classList.add('status-rejected');
        } else if (project.hasPending) {
            projectElement.classList.add('status-pending');
        }
    }

    // 辅助函数：将状态文本转换为数字值
    /*function calculateMD5(file) {
        return new Promise((resolve, reject) => {
            const blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice;
            const chunkSize = 2 * 1024 * 1024; // 2MB chunks (adjust as needed)
            const chunks = Math.ceil(file.size / chunkSize);
            let currentChunk = 0;
            const spark = new SparkMD5.ArrayBuffer();
            const fileReader = new FileReader();

            fileReader.onload = function (e) {
                spark.append(e.target.result); // Append chunk to MD5 calculation
                currentChunk++;

                if (currentChunk < chunks) {
                    loadNextChunk();
                } else {
                    resolve(spark.end()); // Return final MD5 hash
                }
            };

            fileReader.onerror = function () {
                reject(new Error('Failed to read file chunk'));
            };

            function loadNextChunk() {
                const start = currentChunk * chunkSize;
                const end = (start + chunkSize > file.size) ? file.size : start + chunkSize;
                fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));
            }

            loadNextChunk(); // Start reading the first chunk
        });
    }*/
    // 文件上传函数
    function uploadFile(projectId, fileType, file, fileElement, isGpUpload = false) {
        // 获取模板文件名称
        let templateFileName = '';
        const fileNameElem = fileElement.querySelector('.file-name');
        if (fileNameElem) {
            // 获取如 "1. 申请表" 格式的文件名
            const fullText = fileNameElem.textContent;
            // 提取文件名部分（去掉序号和点）
            const match = fullText.match(/^\d+\.\s+(.*?)$/);
            if (match && match[1]) {
                templateFileName = match[1].trim() + '.pdf'; // 添加PDF扩展名
            }
        }

        // 如果没有获取到模板名称，使用默认名称
        if (!templateFileName) {
            templateFileName = '文件_' + fileType + '.pdf';
        }

        // 使用模板名称重新创建文件对象以修改文件名
        const newFile = new File([file], templateFileName, { type: file.type });

        const formData = new FormData();
        formData.append('bao_id', projectId);
        formData.append('file_type', fileType);
        formData.append('file', newFile); // 使用新的文件对象

        if (file.size > 100 * 1024 * 1024) {
            showNotification('文件大小不能超过100MB', true);
            return;
        }
        if (file.size == 0) {
            showNotification('文件无数据，请重新选择', true);
            return;
        }

        // 创建进度条容器
        const progressContainer = document.createElement('div');
        progressContainer.className = 'progress-container';
        progressContainer.style.width = '100%';
        progressContainer.style.height = '5px';
        progressContainer.style.backgroundColor = '#f3f3f3';
        progressContainer.style.borderRadius = '5px';
        progressContainer.style.marginTop = '10px';
        progressContainer.style.overflow = 'hidden';

        const progressBar = document.createElement('div');
        progressBar.className = 'progress-bar';
        progressBar.style.height = '100%';
        progressBar.style.width = '0%';
        progressBar.style.backgroundColor = '#4caf50';
        progressBar.style.transition = 'width 0.3s';

        progressContainer.appendChild(progressBar);

        // 添加进度条到文件元素
        const fileActions = fileElement.querySelector('.file-actions');
        fileActions.appendChild(progressContainer);

        // 显示上传中状态
        fileElement.classList.add('file-uploading');

        const xhr = new XMLHttpRequest();
        xhr.open('POST', `${API_BASE_URL}/upload`, true);
        xhr.setRequestHeader('Authorization', `Bearer ${token}`);
        xhr.timeout = 300000;//5分钟

        // 添加进度停滞检测变量
        let progressTimer;
        let lastLoaded = 0;

        // 添加进度事件监听
        xhr.upload.onprogress = function (e) {
            if (e.lengthComputable) {
                const percentComplete = Math.round((e.loaded / e.total) * 100);
                const progressBar = fileElement.querySelector('.progress-bar');
                if (progressBar) {
                    progressBar.style.width = percentComplete + '%';
                }

                // 重置停滞检测计时器
                clearTimeout(progressTimer);
                if (e.loaded > lastLoaded) {
                    lastLoaded = e.loaded;
                    // 如果10秒内没有新进度，则认为上传已停滞
                    progressTimer = setTimeout(function () {
                        xhr.abort();
                        fileElement.classList.remove('file-uploading');
                        const progressContainer = fileElement.querySelector('.progress-container');
                        if (progressContainer) {
                            progressContainer.remove();
                        }
                        showNotification('上传已停滞，可能是网络不稳定导致', true);
                    }, 10000);
                }
            }
        };
        // 添加中止处理
        xhr.onabort = function () {
            if (isGpUpload) {
                isGpUploading = false;
            }
            fileElement.classList.remove('file-uploading');
            const progressContainer = fileElement.querySelector('.progress-container');
            if (progressContainer) {
                progressContainer.remove();
            }
            showNotification('上传已被中断', true);
        };

        // 增强超时处理
        xhr.ontimeout = function () {
            if (isGpUpload) {
                isGpUploading = false;
            }
            clearTimeout(progressTimer);
            fileElement.classList.remove('file-uploading');
            const progressContainer = fileElement.querySelector('.progress-container');
            if (progressContainer) {
                progressContainer.remove();
            }
            showNotification('上传超时，请重试或压缩文件后再上传', true);
        };
        xhr.onload = function () {
            if (isGpUpload) {
                isGpUploading = false;
            }
            clearTimeout(progressTimer); // 清除进度停滞检测
            fileElement.classList.remove('file-uploading');
            // 完成后移除进度条
            const progressContainer = fileElement.querySelector('.progress-container');
            if (progressContainer) {
                progressContainer.remove();
            }

            if (xhr.status >= 200 && xhr.status < 300) {
                try {
                    const data = JSON.parse(xhr.responseText);
                    if (data.msg === 'File uploaded successfully') {
                        showNotification('文件上传成功！');

                        // 更新文件状态显示
                        const fileStatus = fileElement.querySelector('.file-status');
                        if (fileStatus) {
                            fileStatus.textContent = '审核中';
                            fileStatus.className = 'file-status pending';
                        }

                        // 获取文件信息
                        const md5 = '';
                        const currentTimestamp = Math.floor(Date.now() / 1000);

                        // 统一更新项目数据中的文件状态
                        updateProjectFileStatus(projectId, fileType, 0, null, templateFileName, md5, false);


                        /*calculateMD5(newFile).then(md5 => { //
                        }).catch(err => {
                        });*/

                        // 更新按钮状态
                        const uploadBtn = fileElement.querySelector('.upload-btn');
                        uploadBtn.classList.add('hidden');

                        const uploadgpBtn = fileElement.querySelector('.upload-gp-btn');
                        uploadgpBtn.classList.add('hidden');

                        const pendingBtn = fileElement.querySelector('.pending-btn');
                        pendingBtn.classList.remove('hidden');

                        // 更新文件名显示
                        const fileNameDisplay = fileElement.querySelector('.file-actions .file-name');
                        if (!fileNameDisplay) {
                            const fileActions = fileElement.querySelector('.file-actions');
                            const nameDiv = document.createElement('div');
                            nameDiv.className = 'file-name';
                            nameDiv.textContent = `已上传: ${templateFileName}`;
                            fileActions.insertBefore(nameDiv, fileActions.firstChild);
                        } else {
                            fileNameDisplay.textContent = `已上传: ${templateFileName}`;
                        }
                    } else {
                        showNotification('文件上传失败: ' + data.msg, true);
                    }
                } catch (e) {
                    showNotification('文件上传失败: 无效的响应', true);
                }
            } else {
                try {
                    const data = JSON.parse(xhr.responseText);
                    showNotification('文件上传失败: ' + (data.msg || xhr.statusText), true);
                } catch (e) {
                    showNotification('文件上传失败: ' + xhr.statusText, true);
                }
            }
        };

        xhr.onerror = function (e) {
            if (isGpUpload) {
                isGpUploading = false;
            }
            clearTimeout(progressTimer);
            fileElement.classList.remove('file-uploading');
            // 错误时移除进度条
            const progressContainer = fileElement.querySelector('.progress-container');
            if (progressContainer) {
                progressContainer.remove();
            }
            showNotification('文件上传失败，网络连接不稳定或服务器拒绝请求', true);
        };

        xhr.send(formData);
    }

    function downloadFile(projectId, file_lx) {
        if (!file_lx) {
            showNotification('未提供文件ID，无法下载', true);
            return;
        }

        // 检查是否有下载正在进行中
        if (isDownloading) {
            // 创建一个特殊的固定通知，不会干扰下载进度条
            const existingDownloadAlert = document.getElementById('download-in-progress-alert');
            if (!existingDownloadAlert) {
                const downloadAlert = document.createElement('div');
                downloadAlert.id = 'download-in-progress-alert';
                downloadAlert.style.position = 'fixed';
                downloadAlert.style.top = '80px'; // 位置在主通知下方
                downloadAlert.style.right = '20px';
                downloadAlert.style.padding = '15px 25px';
                downloadAlert.style.borderRadius = '4px';
                downloadAlert.style.backgroundColor = '#e74c3c';
                downloadAlert.style.color = 'white';
                downloadAlert.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
                downloadAlert.style.zIndex = '1001'; // 确保在主通知上方
                downloadAlert.textContent = '有文件正在下载中，请稍后再试';
                document.body.appendChild(downloadAlert);

                // 3秒后自动移除
                setTimeout(() => {
                    if (downloadAlert.parentNode) {
                        downloadAlert.parentNode.removeChild(downloadAlert);
                    }
                }, 3000);
            }
            return;
        }

        // 设置下载状态为正在下载
        isDownloading = true;

        // 创建固定位置的下载状态显示，防止鼠标移动导致消失
        // 先移除已存在的通知，避免叠加
        const existingNotification = document.getElementById('fixed-download-notification');
        if (existingNotification) {
            document.body.removeChild(existingNotification);
        }

        // 创建新的固定下载通知
        const fixedNotification = document.createElement('div');
        fixedNotification.id = 'fixed-download-notification';
        fixedNotification.style.position = 'fixed';
        fixedNotification.style.bottom = '20px';
        fixedNotification.style.left = 'auto';
        fixedNotification.style.transform = 'none';
        fixedNotification.style.right = '20px'; // 设置距离右侧 20px
        fixedNotification.style.bottom = '20px'; // 保持距离底部 20px
        fixedNotification.style.padding = '15px 25px';
        fixedNotification.style.borderRadius = '4px';
        fixedNotification.style.backgroundColor = '#2980b9';
        fixedNotification.style.color = 'white';
        fixedNotification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
        fixedNotification.style.zIndex = '2000';
        fixedNotification.style.minWidth = '300px';
        fixedNotification.style.textAlign = 'center';
        fixedNotification.innerHTML = '<span class="spinner"></span> 文件下载中...';

        // 创建进度条容器并添加到通知中
        const progressContainer = document.createElement('div');
        progressContainer.className = 'download-progress-container';
        progressContainer.style.width = '100%';
        progressContainer.style.height = '8px'; // 稍微加粗进度条
        progressContainer.style.backgroundColor = 'rgba(255,255,255,0.3)';
        progressContainer.style.borderRadius = '5px';
        progressContainer.style.marginTop = '10px';
        progressContainer.style.overflow = 'hidden';

        const progressBar = document.createElement('div');
        progressBar.className = 'download-progress-bar';
        progressBar.style.height = '100%';
        progressBar.style.width = '0%';
        progressBar.style.backgroundColor = '#ffffff';
        progressBar.style.transition = 'width 0.3s';

        progressContainer.appendChild(progressBar);
        fixedNotification.appendChild(progressContainer);
        document.body.appendChild(fixedNotification);

        // 保持原有通知系统的状态，防止修改过多代码
        const notification = document.getElementById('notification');
        notification.style.display = 'none';

        // 使用GET请求，参数附加在URL上
        let contentDisposition;

        // 使用XHR进行下载以跟踪进度
        const xhr = new XMLHttpRequest();
        xhr.open('GET', `${API_BASE_URL}/download?bao_id=${projectId}&file_lx=${file_lx}`, true);
        xhr.responseType = 'blob';
        xhr.setRequestHeader('Authorization', `Bearer ${token}`);

        // 进度监听
        xhr.onprogress = function (e) {
            if (e.lengthComputable) {
                const percentComplete = Math.round((e.loaded / e.total) * 100);
                const progressBar = document.querySelector('#fixed-download-notification .download-progress-bar');
                if (progressBar) {
                    progressBar.style.width = percentComplete + '%';
                    // 更新下载百分比文本
                    const fixedNotification = document.getElementById('fixed-download-notification');
                    if (fixedNotification) {
                        fixedNotification.innerHTML = `<span class="spinner"></span> 文件下载中... ${percentComplete}%`;
                        // 重新添加进度条，因为innerHTML会覆盖
                        fixedNotification.appendChild(progressContainer);
                    }
                }
            }
        };

        // 创建一个Promise包装XHR
        const fetchPromise = new Promise((resolve, reject) => {
            xhr.onload = function () {
                if (xhr.status >= 200 && xhr.status < 300) {
                    // 获取content-disposition头
                    try {
                        contentDisposition = xhr.getResponseHeader('content-disposition');
                    } catch (e) {
                        contentDisposition = null;
                    }
                    resolve(xhr.response);
                } else {
                    alert('文件下载失败: 状态码:' + xhr.status);
                    reject(new Error('下载失败'));
                }
            };

            xhr.onerror = function () {
                reject(new Error('网络错误'));
            };

            xhr.send();
        });

        fetchPromise
            .then(blob => {
                // 和原来的代码一样处理blob数据
                return blob;
            })
            .then(blob => {

                // 从响应头获取文件名或使用备选文件名
                let filename = 'downloaded_file';

                if (contentDisposition) {
                    try {
                        const match = contentDisposition.match(/filename="?([^"]+)"?/);
                        if (match && match[1]) {
                            filename = match[1];
                        }
                    } catch (e) {
                    }
                }

                // 如果没有从响应头获取到文件名，使用项目和文件ID生成文件名
                if (filename === 'downloaded_file') {
                    // 查找当前文件名称
                    const reviewItem = document.querySelector(`.review-item[data-file-id="${file_lx}"]`);
                    if (reviewItem) {
                        const fileNameElem = reviewItem.querySelector('.file-name');
                        if (fileNameElem) {
                            // 获取文件名称（如 "1. 申请表"）并去掉编号
                            const fullName = fileNameElem.textContent.trim();
                            const match = fullName.match(/^\d+\.\s+(.*?)$/);
                            if (match && match[1]) {
                                filename = match[1].trim() + '.pdf';
                            } else {
                                filename = '文件_' + file_lx + '.pdf';
                            }
                        }
                    }
                }

                // 检测是否为移动端
                const isMobile = window.innerWidth <= 768;

                // 创建下载链接
                const url = window.URL.createObjectURL(blob);

                if (isMobile) {
                    // 移动端：使用PDF查看器模态框
                    showMobilePdfViewer(url, filename);
                } else {
                    // 桌面端：在新标签页打开并下载
                    const a = document.createElement('a');
                    a.href = url;
                    a.target = '_blank';
                    a.style.display = 'none';
                    document.body.appendChild(a);
                    a.click();
                    /*a.download = filename;
                    a.click();
                    document.body.removeChild(a);*/

                    // 清理链接
                    /*setTimeout(() => {
                        window.URL.revokeObjectURL(url);
                        if (document.body.contains(a)) {
                            document.body.removeChild(a);
                        }
                    }, 1000);*/
                }
            })
            .catch(error => {
                showNotification('文件下载失败: ' + error.msg, true);
            })
            .finally(() => {
                // 延迟三秒后隐藏下载通知，确保用户能看到下载完成的状态
                // 更新固定通知为"下载完成"状态
                const fixedNotification = document.getElementById('fixed-download-notification');
                if (fixedNotification) {
                    fixedNotification.style.backgroundColor = '#27ae60'; // 绿色表示完成
                    fixedNotification.innerHTML = '<span style="font-weight:bold">✓</span> 文件下载完成';
                    // 显示100%进度
                    const completedProgressContainer = document.createElement('div');
                    completedProgressContainer.style.width = '100%';
                    completedProgressContainer.style.height = '8px';
                    completedProgressContainer.style.backgroundColor = 'rgba(255,255,255,0.3)';
                    completedProgressContainer.style.borderRadius = '5px';
                    completedProgressContainer.style.marginTop = '10px';
                    completedProgressContainer.style.overflow = 'hidden';

                    const completedProgressBar = document.createElement('div');
                    completedProgressBar.style.height = '100%';
                    completedProgressBar.style.width = '100%';
                    completedProgressBar.style.backgroundColor = '#ffffff';

                    completedProgressContainer.appendChild(completedProgressBar);
                    fixedNotification.appendChild(completedProgressContainer);
                }

                setTimeout(() => {
                    // 移除固定通知
                    if (fixedNotification && fixedNotification.parentNode) {
                        fixedNotification.parentNode.removeChild(fixedNotification);
                    }

                    notification.style.display = 'none';
                    // 重置下载状态
                    isDownloading = false;

                    // 移除任何可能存在的下载冲突提示
                    const downloadAlert = document.getElementById('download-in-progress-alert');
                    if (downloadAlert && downloadAlert.parentNode) {
                        downloadAlert.parentNode.removeChild(downloadAlert);
                    }
                }, 1000); // 延长到1秒，让用户更清楚地看到下载完成状态
            });
    }

    // 检测浏览器PDF支持
    function detectPdfSupport() {
        const userAgent = navigator.userAgent.toLowerCase();
        const isIOS = /iphone|ipad|ipod/.test(userAgent);
        const isSafari = /safari/.test(userAgent) && !/chrome/.test(userAgent);
        const isAndroid = /android/.test(userAgent);
        const isChrome = /chrome/.test(userAgent);
        const isFirefox = /firefox/.test(userAgent);

        // iOS Safari 和大部分移动端浏览器不支持iframe PDF
        if (isIOS || isSafari) {
            return 'no-support';
        }

        // Android Chrome 通常支持
        if (isAndroid && isChrome) {
            return 'partial-support';
        }

        // Firefox 移动端支持有限
        if (isAndroid && isFirefox) {
            return 'limited-support';
        }

        // 其他情况尝试支持
        return 'try-support';
    }

    // 移动端PDF查看器函数
    function showMobilePdfViewer(pdfUrl, filename) {
        const modal = document.getElementById('mobile-pdf-viewer');
        const modalTitle = document.getElementById('mobilePdfViewerLabel');
        const iframe = document.getElementById('pdf-iframe');
        const fallback = modal.querySelector('.pdf-fallback');
        const downloadBtn = document.getElementById('pdf-download-btn');
        const openNewTabBtn = document.getElementById('pdf-open-new-tab');
        const downloadFallbackBtn = document.getElementById('pdf-download-fallback');
        const openFallbackBtn = document.getElementById('pdf-open-fallback');

        // 设置标题
        modalTitle.textContent = `查看文件 - ${filename}`;

        // 清理之前的加载状态
        const existingLoading = modal.querySelector('.pdf-loading');
        if (existingLoading) {
            existingLoading.remove();
        }

        // 检测PDF支持
        const pdfSupport = detectPdfSupport();

        if (pdfSupport === 'no-support') {
            // 不支持iframe PDF，直接显示备用方案
            iframe.style.display = 'none';
            fallback.style.display = 'block';

            // 更新备用方案的提示文本
            const fallbackTitle = fallback.querySelector('h5');
            const fallbackText = fallback.querySelector('p');
            if (fallbackTitle) fallbackTitle.textContent = '您的浏览器不支持在线预览PDF';
            if (fallbackText) fallbackText.textContent = '请选择下载文件到设备查看，或在新标签页中打开：';
        } else {
            // 尝试在iframe中加载PDF
            iframe.style.display = 'block';
            fallback.style.display = 'none';

            // 显示加载状态
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'pdf-loading';
            loadingDiv.innerHTML = `
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2">正在加载PDF文件...</div>
            `;
            modal.querySelector('.pdf-viewer-content').appendChild(loadingDiv);

            // 尝试加载PDF
            iframe.src = pdfUrl;

            // 设置超时时间（根据支持程度调整）
            const timeoutDuration = pdfSupport === 'partial-support' ? 8000 : 5000;
            const timeout = setTimeout(() => {
                iframe.style.display = 'none';
                fallback.style.display = 'block';
                loadingDiv.remove();

                // 更新备用方案提示
                const fallbackTitle = fallback.querySelector('h5');
                const fallbackText = fallback.querySelector('p');
                if (fallbackTitle) fallbackTitle.textContent = 'PDF加载超时';
                if (fallbackText) fallbackText.textContent = '网络较慢或浏览器不支持在线预览，请选择其他方式查看：';
            }, timeoutDuration);

            // PDF加载成功
            iframe.onload = function () {
                clearTimeout(timeout);
                loadingDiv.remove();
            };

            // PDF加载失败
            iframe.onerror = function () {
                clearTimeout(timeout);
                iframe.style.display = 'none';
                fallback.style.display = 'block';
                loadingDiv.remove();

                // 更新备用方案提示
                const fallbackTitle = fallback.querySelector('h5');
                const fallbackText = fallback.querySelector('p');
                if (fallbackTitle) fallbackTitle.textContent = 'PDF加载失败';
                if (fallbackText) fallbackText.textContent = '无法在浏览器中显示此PDF文件，请选择其他方式查看：';
            };
        }

        // 绑定按钮事件
        const handleDownload = () => {
            const a = document.createElement('a');
            a.href = pdfUrl;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            showNotification('文件已下载到您的设备');
        };

        const handleOpenNewTab = () => {
            const a = document.createElement('a');
            a.href = pdfUrl;
            a.target = '_blank';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        };

        // 移除之前的事件监听器
        downloadBtn.replaceWith(downloadBtn.cloneNode(true));
        openNewTabBtn.replaceWith(openNewTabBtn.cloneNode(true));
        downloadFallbackBtn.replaceWith(downloadFallbackBtn.cloneNode(true));
        openFallbackBtn.replaceWith(openFallbackBtn.cloneNode(true));

        // 重新获取元素引用
        const newDownloadBtn = document.getElementById('pdf-download-btn');
        const newOpenNewTabBtn = document.getElementById('pdf-open-new-tab');
        const newDownloadFallbackBtn = document.getElementById('pdf-download-fallback');
        const newOpenFallbackBtn = document.getElementById('pdf-open-fallback');

        // 绑定新的事件监听器
        newDownloadBtn.addEventListener('click', handleDownload);
        newOpenNewTabBtn.addEventListener('click', handleOpenNewTab);
        newDownloadFallbackBtn.addEventListener('click', handleDownload);
        newOpenFallbackBtn.addEventListener('click', handleOpenNewTab);

        // 显示模态框
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        // 模态框关闭时清理资源
        modal.addEventListener('hidden.bs.modal', function () {
            iframe.src = '';
            setTimeout(() => {
                window.URL.revokeObjectURL(pdfUrl);
            }, 1000);
        }, { once: true });
    }

    // 显示通知
    function showNotification(message, isError = false) {
        const notification = document.getElementById('notification');
        notification.textContent = message;
        notification.className = `notification ${isError ? 'error' : ''}`;
        notification.style.display = 'block';

        setTimeout(() => {
            notification.style.display = 'none';
        }, 3000);
    }

    // 搜索功能
    const searchInput = document.getElementById('project-search');
    const clearSearch = document.getElementById('clear-search');

    // 检查搜索输入框是否存在
    if (!searchInput || !clearSearch) {
        // 不要直接返回，继续执行后续代码
    } else {
        // 只有当搜索元素存在时才初始化搜索功能
        initSearchFunctionality();
    }

    function initSearchFunctionality() {



        // 强健的文本标准化函数
        function normalizeText(text) {
            if (!text) return '';

            return text
                .normalize('NFKC')  // Unicode标准化
                .replace(/[\u200B-\u200D\uFEFF]/g, '')  // 移除零宽字符
                .replace(/\s+/g, ' ')  // 标准化空格
                .trim()
                .toLowerCase();
        }

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }



        // 搜索执行函数
        function performSearch(inputValue) {
            const searchTerm = normalizeText(inputValue);

            // 检查是否已选择城市和项目类型
            if (!currentCity || !currentType) {
                return;
            }

            // 获取基础列表 - 根据是否有激活的状态过滤
            let baseList;
            if (activeStatusFilter && currentCity && currentType) {
                // 如果有激活的状态过滤，使用全部项目进行过滤
                baseList = projectsData[currentCity][currentType] || [];

                // 根据状态过滤
                if (activeStatusFilter === 'pending-submit') {
                    baseList = baseList.filter(p => p.hasPendingSubmit);
                } else if (activeStatusFilter === 'rejected') {
                    baseList = baseList.filter(p => p.hasRejected);
                } else if (activeStatusFilter === 'pending') {
                    baseList = baseList.filter(p => p.hasPending);
                }
            } else {
                // 否则使用完整的项目列表
                baseList = projectsData[currentCity][currentType] || [];
            }

            // 如果基础列表为空，无法进行搜索
            if (baseList.length === 0) {
                return;
            }

            if (searchTerm === '') {
                // 如果搜索词为空，恢复到基础列表
                currentProjectList = baseList;
                currentPage = 1;
                totalPages = Math.ceil(currentProjectList.length / itemsPerPage);
                renderProjectList();
                renderPagination();
                return;
            }

            const filteredProjects = baseList.filter(project => {
                // 基本搜索条件：项目名称和编号
                let nameMatch = false;
                let codeMatch = false;

                if (project.bao_name) {
                    const projectName = normalizeText(project.bao_name);
                    nameMatch = projectName.includes(searchTerm);
                }

                if (project.bao_bm) {
                    const projectCode = normalizeText(project.bao_bm);
                    codeMatch = projectCode.includes(searchTerm);
                }

                // 增加用户户号搜索
                let userCodeMatch = false;
                if (project.usercode) {
                    const userCode = normalizeText(project.usercode.toString());
                    userCodeMatch = userCode.includes(searchTerm);
                }

                // 返回任一条件匹配的结果
                return nameMatch || codeMatch || userCodeMatch;
            });

            totalPages = Math.ceil(filteredProjects.length / itemsPerPage);
            currentPage = 1;
            currentProjectList = filteredProjects;

            renderProjectList();
            renderPagination();
        }

        // 使用防抖的搜索函数
        const debouncedSearch = debounce(performSearch, 300);

        // 输入法状态跟踪
        let isComposing = false;

        searchInput.addEventListener('compositionstart', function () {
            isComposing = true;
        });

        searchInput.addEventListener('compositionend', function () {
            isComposing = false;
            // 输入法结束后立即执行搜索
            performSearch(this.value);
        });

        // 更新清除按钮显示状态
        function updateClearButtonVisibility() {
            if (clearSearch) {
                if (searchInput.value.trim() !== '') {
                    clearSearch.style.display = 'flex';
                } else {
                    clearSearch.style.display = 'none';
                }
            }
        }

        searchInput.addEventListener('input', function () {
            // 更新清除按钮显示状态
            updateClearButtonVisibility();

            // 如果不在输入法组合中，执行搜索
            if (!isComposing) {
                debouncedSearch(this.value);
            }
        });

        // 清除搜索的通用函数
        function clearSearchAndRestore() {
            searchInput.value = '';

            // 更新清除按钮显示状态
            updateClearButtonVisibility();

            // 直接调用搜索函数，传入空字符串来恢复列表
            performSearch('');
        }

        clearSearch.addEventListener('click', function () {
            clearSearchAndRestore();
        });

        // 初始化清除按钮状态
        updateClearButtonVisibility();

    } // 结束 initSearchFunctionality 函数

    // 新增项目按钮事件
    document.getElementById('add-project-btn').addEventListener('click', function () {
        // 使用全局变量获取当前选中的城市和类型
        if (!currentCity || !currentType) {
            alert('请先从左边的导航栏选择地市和项目类型');
            return;
        }

        const city = currentCity;
        const type = currentType;

        // 创建表单弹窗
        const modalHTML = `
<div class="modal fade" id="addProjectModal" tabindex="-1" aria-labelledby="addProjectModalLabel" aria-hidden="true" style="z-index:2000;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addProjectModalLabel">
                    <i class="bi bi-plus-square-fill me-2"></i>新增项目 - ${city} ${type}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="projectForm" class="row g-3">
                    <div class="col-md-6">
                        <label for="projectName" class="form-label">
                            <i class="bi bi-card-heading me-1"></i>项目名称
                        </label>
                        <input type="text" class="form-control" id="projectName" placeholder="请输入项目名称" required>
                    </div>
                    <div class="col-md-6">
                        <label for="projectCode" class="form-label">
                            <i class="bi bi-upc-scan me-1"></i>项目编号
                        </label>
                        <input type="text" class="form-control" id="projectCode" placeholder="请输入项目编号" required>
                    </div>
                    <div class="col-md-6 mt-3">
                        <label for="projectUsercode" class="form-label">
                            <i class="bi bi-hash me-1"></i>户号
                        </label>
                        <input type="text" class="form-control" id="projectUsercode" placeholder="请输入用户户号" required>
                    </div>
                    <div class="col-md-6 mt-3">
                        <label for="projectGds" class="form-label">
                            <i class="bi bi-geo-alt-fill me-1"></i>供电所
                        </label>
                        <select class="form-control" id="projectGds" required>
                            <option value="">请选择供电所</option>
                        </select>
                    </div>
                    <div class="col-md-6 mt-3">
                        <label for="projectHtrl" class="form-label">
                            <i class="bi bi-lightning-charge me-1"></i>合同容量 (kVA)
                        </label>
                        <input type="number" class="form-control" id="projectHtrl" placeholder="请输入合同容量" min="0" step="0.01">
                    </div>
                    <div class="col-md-6 mt-3">
                        <label for="projectGdlx" class="form-label">
                            <i class="bi bi-list-ul me-1"></i>工单类型
                        </label>
                        <select class="form-control" id="projectGdlx">
                            <option value="">请选择工单类型</option>
                            <option value="增容">增容</option>
                            <option value="临时用电">临时用电</option>
                            <option value="新装">新装</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i>取消
                </button>
                <button type="button" class="btn btn-primary" id="submitProject">
                    <i class="bi bi-check-circle me-1"></i>提交
                </button>
            </div>
        </div>
    </div>
</div>
`;


        // 新增项目添加模态框到DOM
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // 初始化模态框
        const modal = new bootstrap.Modal(document.getElementById('addProjectModal'));
        modal.show();

        // 更新供电所选项的函数
        function updateGdsOptions() {
            const gdsSelect = document.getElementById('projectGds');

            // 清空供电所选项
            gdsSelect.innerHTML = '<option value="">请选择供电所</option>';

            // 如果选择了城市且存在对应的供电所数据
            if (city && my_gds[city]) {
                my_gds[city].forEach(gds => {
                    const option = document.createElement('option');
                    option.value = gds.mgtOrgName;
                    option.textContent = gds.mgtOrgName;
                    gdsSelect.appendChild(option);
                });
            }
        }

        // 初始化供电所选项
        updateGdsOptions();

        // 提交按钮事件
        document.getElementById('submitProject').addEventListener('click', function () {
            const projectName = document.getElementById('projectName').value.trim();
            const projectCode = document.getElementById('projectCode').value.trim();
            const projectUsercode = document.getElementById('projectUsercode').value.trim();
            const projectGds = document.getElementById('projectGds').value; // 下拉选择框不需要trim
            const projectHtrl = document.getElementById('projectHtrl').value.trim(); // 合同容量
            const projectGdlx = document.getElementById('projectGdlx').value; // 工单类型

            if (!projectName || !projectCode || !projectUsercode || !projectGds) {
                alert('表单内容不能为空，请确保所有必填字段都已填写');
                return;
            }

            // 验证合同容量
            if (projectHtrl && (isNaN(projectHtrl) || parseFloat(projectHtrl) < 0)) {
                alert('合同容量必须是有效的非负数字');
                return;
            }

            // 这里添加提交到API的逻辑
            fetch(`${API_BASE_URL}/addbao`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    bao_name: projectName,
                    bao_lx: type,
                    bao_bm: projectCode,
                    bao_city: city,
                    usercode: projectUsercode,
                    gds: projectGds,
                    htrl: projectHtrl ? parseFloat(projectHtrl) : null, // 合同容量
                    gdlx: projectGdlx || null // 工单类型
                })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.msg === "新增成功") {
                        showNotification('项目添加成功');
                        modal.hide();
                        // 刷新项目列表
                        projectsData[city][type].push({
                            id: data.bao_id, //这里换成返回的ID
                            bao_name: projectName,
                            bao_lx: type,
                            bao_bm: projectCode,
                            bao_city: city,
                            usercode: projectUsercode,
                            gds: projectGds,
                            htrl: projectHtrl ? parseFloat(projectHtrl) : null, // 合同容量
                            gdlx: projectGdlx || null, // 工单类型
                            // 时间字段初始化为空
                            sj_ywsl: null,
                            sj_xckc: null,
                            sj_fadf: null,
                            sj_jgys: null,
                            sj_zbsd: null,
                            sj_gdsj: null,
                            files: [],
                            is_approve: false,
                            is_reject: false,
                            is_submit: true,
                            bao_state: false,
                        });
                        // 判断项目编号是否是4开头的，如果是，则判断是否是2024年6月1日之前，如果是，则添加一个文件
                        if (type == '高压用户' || type == '华宇高压用户') {
                            if (projectCode.substring(0, 1) == "4") {
                                times = projectCode.substring(2, 8);
                                maxtimes = 240601;
                            }
                            else {
                                times = projectCode.substring(0, 8);
                                maxtimes = 20240601;
                            }
                            if (parseInt(times) < maxtimes) {
                                projectsData[city][type].forEach(item => {
                                    if (item.bao_bm == projectCode) {
                                        item.files.push({
                                            file_lx: 4,
                                            file_name: "2024年6月1日前的项目此项无需收资",
                                            file_state: 1
                                        });
                                    }
                                });
                            }
                        }
                        processData(projectsData);
                        initNavigation(projectsData);
                        // 通过类名和文本内容查找元素

                        loadProjects(projectsData, city, type);

                        // 可选：自动选中新添加的项目

                        const cityElements = document.querySelectorAll('.city-name');
                        cityElements.forEach(cityElement => {
                            if (cityElement.textContent.trim() === city) {
                                // 点击地市以展开选项
                                cityElement.click();

                                // 2. 确保地市选项完全展开后，再点击类型
                                setTimeout(() => {
                                    // 找到当前地市下的所有类型选项
                                    const cityItem = cityElement.closest('.city-item');
                                    const typeElements = cityItem.querySelectorAll('.option-item');

                                    typeElements.forEach(typeElement => {
                                        const typeName = typeElement.querySelector('div:first-child').textContent.trim();
                                        if (typeName === type) {
                                            // 点击类型以加载项目
                                            typeElement.click();

                                            // 3. 确保项目加载完成后，选中新项目
                                            setTimeout(() => {
                                                const newProjectElement = document.querySelector(`.project-item[data-id="${data.bao_id}"]`);
                                                if (newProjectElement) {
                                                    newProjectElement.click();
                                                }
                                            }, 200); // 等待项目加载
                                        }
                                    });
                                }, 200); // 等待地市选项展开
                            }
                        });


                    } else {
                        alert('项目添加失败: ' + data.msg);
                    }
                })
                .catch(error => {
                    alert('项目添加失败，请重试');
                });
        });

        // 模态框关闭时移除
        document.getElementById('addProjectModal').addEventListener('hidden.bs.modal', function () {
            this.remove();
        });
    });

    // 移动端导航功能
    function initMobileNavigation() {
        const mobileNav = document.getElementById('mobile-nav');
        const screens = document.querySelectorAll('.screen');
        const navItems = document.querySelectorAll('.mobile-nav .nav-item');

        if (!mobileNav) return; // 如果没有移动端导航，直接返回

        // 检查是否为移动端
        function isMobile() {
            return window.innerWidth <= 768;
        }

        // 切换屏幕显示
        function switchScreen(targetScreenId) {
            // 隐藏所有屏幕
            screens.forEach(screen => {
                screen.classList.remove('active');
            });

            // 显示目标屏幕
            const targetScreen = document.getElementById(targetScreenId);
            if (targetScreen) {
                targetScreen.classList.add('active');
            }

            // 更新导航项状态
            navItems.forEach(item => {
                item.classList.remove('active');
                if (item.dataset.screen === targetScreenId) {
                    item.classList.add('active');
                }
            });

            // 更新状态指示器
            const statusIndicator = document.getElementById('mobile-status-indicator');
            if (statusIndicator) {
                const statusDots = statusIndicator.querySelectorAll('.status-dot');
                statusDots.forEach(dot => {
                    dot.classList.remove('active');
                    if (dot.dataset.screen === targetScreenId) {
                        dot.classList.add('active');
                    }
                });
            }

            // 滚动到顶部
            window.scrollTo(0, 0);
        }

        // 绑定导航项点击事件
        navItems.forEach(item => {
            item.addEventListener('click', function () {
                const targetScreen = this.dataset.screen;
                switchScreen(targetScreen);
            });
        });

        // 窗口大小改变时的处理
        function handleResize() {
            const statusIndicator = document.getElementById('mobile-status-indicator');

            if (isMobile()) {
                // 移动端：确保只有一个屏幕显示
                const activeScreen = document.querySelector('.screen.active');
                if (!activeScreen) {
                    // 如果没有激活的屏幕，默认显示第一个
                    switchScreen('nav-screen');
                }

                // 显示状态指示器
                if (statusIndicator) {
                    statusIndicator.style.display = 'flex';
                }
            } else {
                // 桌面端：显示所有屏幕
                screens.forEach(screen => {
                    screen.classList.add('active');
                });

                // 隐藏状态指示器
                if (statusIndicator) {
                    statusIndicator.style.display = 'none';
                }
            }
        }

        // 初始化
        handleResize();

        // 监听窗口大小变化
        window.addEventListener('resize', handleResize);

        // 添加触摸滑动支持（可选）
        let startX = 0;
        let startY = 0;

        document.addEventListener('touchstart', function (e) {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });

        document.addEventListener('touchend', function (e) {
            if (!isMobile()) return;

            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            const diffX = startX - endX;
            const diffY = startY - endY;

            // 只有水平滑动距离大于垂直滑动距离且超过50px时才触发
            if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                const currentActiveNav = document.querySelector('.mobile-nav .nav-item.active');
                const currentIndex = Array.from(navItems).indexOf(currentActiveNav);

                if (diffX > 0 && currentIndex < navItems.length - 1) {
                    // 向左滑动，显示下一个屏幕
                    navItems[currentIndex + 1].click();
                } else if (diffX < 0 && currentIndex > 0) {
                    // 向右滑动，显示上一个屏幕
                    navItems[currentIndex - 1].click();
                }
            }
        });
    }

    // 初始化移动端导航
    initMobileNavigation();

    // 滚动条透明化控制 - 滚动时显示滚动条
    function initScrollbarVisibility() {
        let scrollTimeout;

        // 为所有可滚动元素添加滚动事件监听
        const scrollableElements = document.querySelectorAll('.screen, .card-body, .userinfo-content');

        scrollableElements.forEach(element => {
            element.addEventListener('scroll', function () {
                // 添加滚动中的类名
                this.classList.add('scrolling');

                // 清除之前的定时器
                clearTimeout(scrollTimeout);

                // 设置新的定时器，滚动停止后隐藏滚动条
                scrollTimeout = setTimeout(() => {
                    this.classList.remove('scrolling');
                }, 1000); // 1秒后隐藏滚动条
            });
        });
    }

    // 初始化滚动条可见性控制
    initScrollbarVisibility();

    // 全局暴露函数，供其他地方调用
    window.toggleUserinfoSidebar = toggleUserinfoSidebar;
    window.initUserinfoSidebar = initUserinfoSidebar;

});